{"name": "erp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "apexcharts": "^4.7.0", "formik": "^2.4.6", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "react-router-dom": "^7.1.3", "react-select": "^5.10.1", "react-toastify": "^11.0.3", "redux-persist": "^6.0.0", "sweetalert2": "^11.22.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5"}}
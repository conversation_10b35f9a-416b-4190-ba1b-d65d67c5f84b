import { toast } from "react-toastify";

// utils/errorHandler.js
export const handleApiErrors = (error) => {
  if (!error) {
    toast.error("Network error. Please try again later.", {
      position: "top-right",
      autoClose: 3000, // in milliseconds
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  }
  if (error.data?.status == 500 && error?.status == 401) {
    const error_status = error.data?.status;
    alert(error_status);
  }
  if (error?.status == 422) {
    const error_status = error.data?.status;
    if (error_status == 400 || error_status == 401) {
      let err_list = error.data.errors;
      if (err_list.length > 0) {
        err_list.forEach((erro) => {
          toast.error(erro.message, {
            position: "top-right",
            autoClose: 3000, // in milliseconds
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
          });
        });
      }
    }
  }
  if (error?.status == 400) {
    if (error.data.message != "") {
      toast.error(error.data.message, {
        position: "top-right",
        autoClose: 3000, // in milliseconds
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    }
  }
};

import { toast } from "react-toastify";

// utils/errorHandler.js
export const handleApiSuccess = (data) => {
  if (data?.status == 200) {
    const api_status = data?.status;
    if (api_status == 200) {
      toast.success(data.message, {
        position: "top-right",
        autoClose: 3000, // in milliseconds
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    }
  }
};

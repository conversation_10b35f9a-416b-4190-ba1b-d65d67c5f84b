import { toast } from "react-toastify";

export const handleToast = (type, message) => {
  if (type == "success") {
    toast.success(message, {
      position: "top-right",
      autoClose: 3000, // in milliseconds
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  } else {
    toast.error(message, {
      position: "top-right",
      autoClose: 3000, // in milliseconds
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  }
};

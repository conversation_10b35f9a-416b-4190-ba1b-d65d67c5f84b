import { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setSidebarValue,
  setThemeBoxLayout,
  setThemeDarkLight,
  setThemeDirection,
  setThemeLayout,
  setCardLayout,
  setColorTheme,
} from "../feature/slice/appConfigSlice";

const useThemeSettings = () => {
  const {
    themeLayout,
    sidebarValue,
    themeDarkLight,
    themeDirection,
    themeBoxLayout,
    cardLayout,
    colorTheme,
  } = useSelector((s) => s.appConfig);

  const dispatch = useDispatch();
  /* ******************* Vertical Theme Manage ********************** */
  const VerticalThemeManage = () => {
    const isSidebar = document.getElementsByClassName("side-mini-panel");
    if (isSidebar.length > 0) {
      const findMatchingElement = () => {
        const currentUrl = window.location.href;
        return [...document.querySelectorAll("#sidebarnav a")].find(
          (anchor) => anchor.href === currentUrl
        );
      };

      const elements = findMatchingElement();
      if (elements) elements.classList.add("active");

      document.querySelectorAll("#sidebarnav a").forEach((link) => {
        link.addEventListener("click", function (e) {
          e.preventDefault();
          const isActive = this.classList.contains("active");
          const parentUl = this.closest("ul");

          if (!isActive) {
            parentUl.querySelectorAll("ul").forEach((submenu) => {
              submenu.classList.remove("in");
            });
            parentUl.querySelectorAll("a").forEach((navLink) => {
              navLink.classList.remove("active");
            });

            const submenu = this.nextElementSibling;
            if (submenu) submenu.classList.add("in");
            this.classList.add("active");
          } else {
            this.classList.remove("active");
            parentUl.classList.remove("active");
            const submenu = this.nextElementSibling;
            if (submenu) submenu.classList.remove("in");
          }
        });
      });

      document
        .querySelectorAll("#sidebarnav > li > a.has-arrow")
        .forEach((link) => {
          link.addEventListener("click", (e) => e.preventDefault());
        });
    }
  };
  /* ******************* End  ********************** */
  /* ******************* Horizontal Theme Manage ********************** */
  const HorizontalThemeManage = () => {
    const findMatchingElement = () => {
      const currentUrl = window.location.href;
      return [
        ...document.querySelectorAll("#sidebarnavh ul#sidebarnav a"),
      ].find((anchor) => anchor.href === currentUrl);
    };

    const elements = findMatchingElement();
    if (elements) elements.classList.add("active");
  };
  /* ******************* End  ********************** */
  const ManageThemeLayout = useCallback(() => {
    switch (themeLayout) {
      case "horizontal":
        HorizontalThemeManage();
        document.documentElement.setAttribute("data-layout", "horizontal");
        break;
      case "vertical":
        VerticalThemeManage();
        document.documentElement.setAttribute("data-layout", "vertical");
        break;
      default:
        break;
    }
  }, [themeLayout]);

  const ManageSidebarType = useCallback(() => {
    switch (sidebarValue) {
      case "full":
        document.body.setAttribute("data-sidebartype", "full");
        document.getElementById("main-wrapper").classList.add("show-sidebar");
        break;
      case "mini-sidebar":
        document.body.setAttribute("data-sidebartype", "mini-sidebar");
        document
          .getElementById("main-wrapper")
          .classList.remove("show-sidebar");
        break;
      default:
        break;
    }
  }, [sidebarValue]);

  const ManageDarkThemeLayout = useCallback(() => {
    if (themeDarkLight === "light") {
      document.documentElement.setAttribute("data-bs-theme", "light");
    } else if (themeDarkLight === "dark") {
      document.documentElement.setAttribute("data-bs-theme", "dark");
    }
  }, [themeDarkLight]);

  const ManageDirectionLayout = useCallback(() => {
    switch (themeDirection) {
      case "ltr":
        document.documentElement.setAttribute("dir", "ltr");
        break;
      case "rtl":
        document.documentElement.setAttribute("dir", "rtl");
        break;
      default:
        break;
    }
  }, [themeDirection]);

  const ManageBoxedLayout = useCallback(() => {
    switch (themeBoxLayout) {
      case "boxed":
        document.documentElement.setAttribute("data-boxed-layout", "boxed");
        break;
      case "full":
        document.documentElement.setAttribute("data-boxed-layout", "full");
        break;
      default:
        break;
    }
  }, [themeBoxLayout]);

  const ManageCardLayout = useCallback(() => {
    switch (cardLayout) {
      case "border":
        document.documentElement.setAttribute("data-card", "border");
        break;
      case "shadow":
        document.documentElement.setAttribute("data-card", "shadow");
        break;
      default:
        break;
    }
  }, [cardLayout]);
  const ManageColorThemeLayout = useCallback(() => {
    document.documentElement.setAttribute("data-color-theme", colorTheme);
  }, [colorTheme]);
  useEffect(() => {
    ManageThemeLayout();
    ManageSidebarType();
    ManageDirectionLayout();
    ManageDarkThemeLayout();
    ManageBoxedLayout();
    ManageCardLayout();
    ManageColorThemeLayout();
  }, [
    ManageThemeLayout,
    ManageSidebarType,
    ManageDirectionLayout,
    ManageDarkThemeLayout,
    ManageBoxedLayout,
    ManageCardLayout,
    ManageColorThemeLayout,
  ]);

  const toggleThemeLayout = () => {
    if (themeLayout == "" || themeLayout == "horizontal") {
      dispatch(setThemeLayout("vertical"));
    } else {
      dispatch(setThemeLayout("horizontal"));
    }
  };
  const toggleSidebarType = () => {
    if (sidebarValue == "" || sidebarValue == "full") {
      dispatch(setSidebarValue("mini-sidebar"));
    } else {
      dispatch(setSidebarValue("full"));
    }
  };

  const toggleThemeDarkLight = () => {
    if (themeDarkLight == "" || themeDarkLight == "dark") {
      dispatch(setThemeDarkLight("light"));
    } else {
      dispatch(setThemeDarkLight("dark"));
    }
  };
  const toggleThemeDirection = () => {
    if (themeDirection == "" || themeDirection == "ltr") {
      dispatch(setThemeDirection("rtl"));
    } else {
      dispatch(setThemeDirection("ltr"));
    }
  };

  const toggleBoxedLayout = () => {
    if (themeBoxLayout == "" || themeBoxLayout == "full") {
      dispatch(setThemeBoxLayout("boxed"));
    } else {
      dispatch(setThemeBoxLayout("full"));
    }
  };

  const toggleCardLayout = () => {
    if (cardLayout == "" || cardLayout == "shadow") {
      dispatch(setCardLayout("border"));
    } else {
      dispatch(setCardLayout("shadow"));
    }
  };
  const toggleColorThemeLayout = () => {
    if (colorTheme == "" || colorTheme == "Blue_Theme") {
      dispatch(setColorTheme("Orange_Theme"));
    } else {
      dispatch(setColorTheme("Blue_Theme"));
    }
  };
  return {
    toggleThemeLayout,
    toggleThemeDirection,
    toggleThemeDarkLight,
    toggleSidebarType,
    toggleBoxedLayout,
    toggleCardLayout,
    toggleColorThemeLayout,
  };
};

export default useThemeSettings;

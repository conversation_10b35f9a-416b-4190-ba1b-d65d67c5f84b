import Swal from 'sweetalert2';

/**
 * Custom hook for showing delete confirmation dialog
 * @param {Object} options - Configuration options for the confirmation dialog
 * @param {string} options.title - Title of the confirmation dialog
 * @param {string} options.text - Text content of the confirmation dialog
 * @param {string} options.confirmButtonText - Text for the confirm button
 * @param {string} options.cancelButtonText - Text for the cancel button
 * @param {string} options.icon - Icon type (warning, error, success, info, question)
 * @returns {Function} showConfirmDelete - Function to show the confirmation dialog
 */
const useConfirmDelete = (options = {}) => {
  const defaultOptions = {
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: 'Yes, delete it!',
    cancelButtonText: 'Cancel',
    reverseButtons: true,
    ...options
  };

  /**
   * Show confirmation dialog and execute callback if confirmed
   * @param {Function} onConfirm - Callback function to execute when user confirms
   * @param {Function} onCancel - Optional callback function to execute when user cancels
   * @returns {Promise} Promise that resolves with the result
   */
  const showConfirmDelete = async (onConfirm, onCancel = null) => {
    try {
      const result = await Swal.fire(defaultOptions);
      
      if (result.isConfirmed) {
        // User clicked confirm
        if (typeof onConfirm === 'function') {
          await onConfirm();
        }
        
        // Show success message
        await Swal.fire({
          title: 'Deleted!',
          text: 'The item has been deleted successfully.',
          icon: 'success',
          confirmButtonColor: '#3085d6',
          timer: 2000,
          timerProgressBar: true
        });
        
        return { isConfirmed: true };
      } else if (result.isDismissed) {
        // User clicked cancel or dismissed
        if (typeof onCancel === 'function') {
          await onCancel();
        }
        
        return { isConfirmed: false, isDismissed: true };
      }
    } catch (error) {
      console.error('Error in confirmation dialog:', error);
      
      // Show error message
      await Swal.fire({
        title: 'Error!',
        text: 'An error occurred while processing your request.',
        icon: 'error',
        confirmButtonColor: '#d33'
      });
      
      return { isConfirmed: false, error: true };
    }
  };

  /**
   * Show simple confirmation dialog without callbacks
   * @param {Object} dynamicOptions - Options to override defaults for this specific call
   * @returns {Promise<boolean>} Promise that resolves to true if confirmed, false otherwise
   */
  const showSimpleConfirm = async (dynamicOptions = {}) => {
    try {
      const mergedOptions = { ...defaultOptions, ...dynamicOptions };
      const result = await Swal.fire(mergedOptions);
      return result.isConfirmed;
    } catch (error) {
      console.error('Error in simple confirmation dialog:', error);
      return false;
    }
  };

  return {
    showConfirmDelete,
    showSimpleConfirm
  };
};

/**
 * Quick delete confirmation with default settings
 * @param {Function} onConfirm - Callback function to execute when user confirms
 * @param {Object} customOptions - Custom options to override defaults
 * @returns {Promise} Promise that resolves with the result
 */
export const quickDeleteConfirm = async (onConfirm, customOptions = {}) => {
  const { showConfirmDelete } = useConfirmDelete(customOptions);
  return await showConfirmDelete(onConfirm);
};

export default useConfirmDelete;

export const formatDate = (date) => {
  const [month, day, year] = date.split("/");
  const newDate = new Date(year, month - 1, day); // Month is 0-based in JavaScript
  return `${newDate.getFullYear()}-${String(newDate.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(newDate.getDate()).padStart(2, "0")}`;
};

export const formatTime = (time) => {
  // Parse the time string (e.g., "1:31:34 PM")
  const [newTime, period] = time.split(" ");
  let [hours, minutes, seconds] = newTime.split(":").map(Number);

  // Convert to 24-hour format
  if (period.toUpperCase() === "PM" && hours !== 12) {
    hours += 12;
  } else if (period.toUpperCase() === "AM" && hours === 12) {
    hours = 0;
  }
  // Format to HH:mm:ss with leading zeros
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
    2,
    "0"
  )}:${String(seconds).padStart(2, "0")}`;
};

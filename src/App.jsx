import Router from "./router/router";
import "./App.css";
import { Provider } from "react-redux";
import { persistor, store } from "./feature/store";
import { PersistGate } from "redux-persist/integration/react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
function App() {
  return (
    <Provider store={store}>
      <PersistGate persistor={persistor}>
        <Router />
      </PersistGate>
      <ToastContainer />
    </Provider>
  );
}

export default App;

$(function (e) {
  "use strict";
  $(".date-inputmask").inputmask({
    alias: "datetime",
    inputFormat: "mm/dd/yyyy",
    placeholder: "mm/dd/yyyy",
  }),
    $(".phone-inputmask").inputmask("(*************"),
    $(".international-inputmask").inputmask("+9(999)999-9999"),
    $(".xphone-inputmask").inputmask("(************* / x999999"),
    $(".purchase-inputmask").inputmask("aaaa 9999-****"),
    $(".cc-inputmask").inputmask("9999 9999 9999 9999"),
    $(".ssn-inputmask").inputmask("***********"),
    $(".isbn-inputmask").inputmask("999-99-999-9999-9"),
    $(".currency-inputmask").inputmask("$9999"),
    $(".percentage-inputmask").inputmask("99%"),
    $(".optional-inputmask").inputmask("(99) 9999[9]-9999"),
    $(".decimal-inputmask").inputmask({
      alias: "decimal",
      radixPoint: ".",
    }),
    $(".email-inputmask").inputmask({
      mask: "*{1,20}[.*{1,20}][.*{1,20}][.*{1,20}]@*{1,20}[*{2,6}][*{1,2}].*{1,}[.*{2,6}][.*{1,2}]",
      greedy: !1,
      onBeforePaste: function (n, a) {
        return (e = e.toLowerCase()).replace("mailto:", "");
      },
      definitions: {
        "*": {
          validator: "[0-9A-Za-z!#$%&'*+/=?^_`{|}~/-]",
          cardinality: 1,
          casing: "lower",
        },
      },
    }),
    $("#num-letter").inputmask("999-AAA"),
    $("#date-time-once").inputmask();
});

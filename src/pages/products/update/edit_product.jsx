import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleCustomError } from "../../../hooks/handleCustomError";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useListAllBrandsQuery } from "../../../feature/api/brandsDataApiSlice";
import FormikField from "../../../components/formikField";
import {
  useCreateUpdateVariationsProductsMutation,
  useEditProductMutation,
  useGetVariationsProductsMutation,
  useSingleProductQuery,
} from "../../../feature/api/productDataApiSlice";
import { useNavigate, useParams } from "react-router-dom";
import { useListAllCategoriesQuery } from "../../../feature/api/categoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../../feature/api/subCategoriesDataApiSlice";
import { useGetAllChildCategoriesQuery } from "../../../feature/api/childCategoriesDataApiSlice";
import { useEffect, useMemo, useState } from "react";
import WebLoader from "../../../components/webLoader";
import { useSelector } from "react-redux";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";

// Define initial values with attribute_id as an array
const initialValues = {
  image: null,
  title: "",
  title_ar: "",
  brand_id: "",
  category_id: "",
  sub_category_id: "",
  child_category_id: "",
  description: "",
  description_ar: "",
  is_publish: "",
  note: "",
  product_slug: "",
  attribute_id: [], // Initialize attribute_id as an empty array
};

// Update validation schema to include attribute_id as an array
const validation = yup.object().shape({
  title: yup.string().required().label("Product Title in English"),
  title_ar: yup.string().required().label("Product Title in Arabic"),
  brand_id: yup.string().required().label("Brand"),
  category_id: yup.string().required().label("Category"),
  sub_category_id: yup.string().label("Sub Category"),
  child_category_id: yup.string().label("Child Category"),
  description: yup.string().label("Description in English"),
  description_ar: yup.string().label("Description in Arabic"),
  is_publish: yup.string().required().label("Status"),
  image: yup.mixed().label("Product Image"),
  note: yup.string().label("Note"),
  attribute_id: yup.array().of(yup.string()).label("Product Variations"), // Validate attribute_id as an array of strings
});

export default function EditProduct() {
  const navigation = useNavigate();
  const { id } = useParams();
  const productId = parseInt(id);
  const activePage = "Products Master";
  const linkHref = "/dashboard";

  // const [selectedBranchId, setSelectedBranchId] = useState("");
  // const [selectedProductId, setSelectedProductId] = useState("");

  /* **************** Start fetching single product data ******************* */
  let { data: singleProduct, isLoading: isProductLoading } = useSingleProductQuery({
    id: productId,
  });

  let { data: singleProductVariations, isLoading: isProductVariationLoading } = useGetVariationsProductsMutation({
    product_id: productId,
  });

  const productData = useMemo(() => {
    return singleProduct?.data || null;
  }, [singleProduct]);

  // Extract attribute_ids from singleProductVariations
  const variationAttributeIds = useMemo(() => {
    if (!singleProductVariations?.data) return [];
    return singleProductVariations.data.map((variation) =>
      variation.attribute_id.toString()
    );
  }, [singleProductVariations]);

  /* **************** Start fetching single product data ******************* */

  useEffect(() => {
    if (isProductLoading || isProductVariationLoading) return;

    if (!singleProductVariations) {
      handleCustomError("Error was found, please try again later!");
      // navigation("/products");
    }

    if (!productData) {
      handleCustomError("Error was found, please try again later!");
      navigation("/products");
    } else {
      setSelectedCategoryId(parseInt(productData?.category_id));
      setSelectedSubCategoryId(parseInt(productData?.sub_category_id));
    }
  }, [
    isProductLoading,
    productData,
    isProductVariationLoading,
    singleProductVariations,
    navigation,
  ]);

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector((state) => state.commonState.generalStatus);
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list General Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + ' (' + values.branch_type_name + ')',
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start List Brand List ******************* */
  const brandResp = useListAllBrandsQuery();
  const brand = brandResp.data?.data || [];
  const brandAry = brand.map((brand) => ({
    value: brand.id,
    label: brand.title,
  }));
  /* **************** End List Brand List ******************* */

  /* **************** Start Category List ******************* */
  const categoryListResp = useListAllCategoriesQuery();
  const categoryList = categoryListResp.data?.data || [];
  const categoriesList = categoryList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End Category List ******************* */

  /* **************** Start Fetch Subcategories Based On Category ******************* */
  const [selectedCategoryId, setSelectedCategoryId] = useState(productData?.category_id || null);
  const { data: subCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: selectedCategoryId },
    { skip: !selectedCategoryId }
  );
  const subCategoriesList = useMemo(() => {
    if (!subCategoriesData?.data?.length) {
      return [];
    }
    return subCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subCategoriesData?.data]);
  /* **************** End Fetch Subcategories Based On Category ******************* */

  /* **************** Start Handle category selection change ******************* */
  const handleCategoryChange = (e) => {
    setSelectedCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle category selection change ******************* */

  /* **************** Start Fetch childCategories Based On SubCategory ******************* */
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(productData?.sub_category_id || null);
  const { data: childCategoriesData } = useGetAllChildCategoriesQuery(
    { sub_category_id: selectedSubCategoryId },
    { skip: !selectedSubCategoryId }
  );
  const childCategoriesList = useMemo(() => {
    if (!childCategoriesData?.data?.length) {
      return [];
    }
    return childCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [childCategoriesData?.data]);
  /* **************** End Fetch childCategories Based On SubCategory ******************* */

  /* **************** Start Handle sub category selection change ******************* */
  const handleSubCategoryChange = (e) => {
    setSelectedSubCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle sub category selection change ******************* */

  // Placeholder for attributesAry (you should replace this with actual attributes data)
  const attributesAry = [
    { value: "1", label: "Size" },
    { value: "2", label: "Color" },
    { value: "3", label: "Material" },
    // Add more attributes as needed
  ];

  // Updated EditProductValues with attribute_id
  const EditProductValues = useMemo(
    () =>
      !productData
        ? { ...initialValues, attribute_id: variationAttributeIds }
        : {
            id: productData?.id || "",
            branch_id: productData?.branch_id || "",
            title: productData?.title || "",
            title_ar: productData?.title_ar || "",
            brand_id: productData?.brand_id || "",
            category_id: productData?.category_id || "",
            sub_category_id: productData?.sub_category_id || "",
            child_category_id: productData?.child_category_id || "",
            description: productData?.description || "",
            description_ar: productData?.description_ar || "",
            is_publish: productData?.is_publish || "",
            note: productData?.note || "",
            product_slug: productData?.slug || "",
            attribute_id: variationAttributeIds, // Pre-select attribute IDs
          },
    [productData, variationAttributeIds]
  );

  // Create/Update Product
  const [handleUpdateProductBasicApi, { isLoading: isLoading }] = useEditProductMutation();
  const handleSubmit = async (body) => {
    try {
      const formData = new FormData();
      for (const [key, value] of Object.entries(body)) {
        if (key === "image" && value) {
          formData.append(key, value);
        } else if (
          key === "is_publish" ||
          key === "category_id" ||
          (key === "sub_category_id" && value !== "") ||
          (key === "child_category_id" && value !== "") ||
          key === "brand_id"
        ) {
          formData.append(key, parseInt(value));
        } else if (key === "attribute_id") {
          // Handle attribute_id as an array
          value.forEach((id) => formData.append("attribute_id[]", id));
        } else {
          formData.append(key, value);
        }
      }
      const resp = await handleUpdateProductBasicApi(formData).unwrap();
      handleApiSuccess(resp);
      handleAssignVariationSubmitFunction({
        product_id: resp.data,
        attribute_id: body.attribute_id,
      });
      setDetailsTabDisplay("block");
      setActiveTab("product-details");
      // setSelectedBranchId(body.branch_id);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  const [handleAssignVariationDataApi, { isLoading: isAssignVariationLoading }] =
    useCreateUpdateVariationsProductsMutation();
  const handleAssignVariationSubmitFunction = async (body) => {
    try {
      const attributeIds = Array.isArray(body.attribute_id)
        ? body.attribute_id.map((id) => parseInt(id))
        : [];
      const updatedBody = {
        ...body,
        product_id: parseInt(body.product_id),
        attribute_id: attributeIds,
      };
      await handleAssignVariationDataApi(updatedBody).unwrap();
      // setSelectedProductId(body.product_id);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Tab Management ******************* */
  const [activeTab, setActiveTab] = useState("product-basics");
  const [detailsTabDisplay, setDetailsTabDisplay] = useState("none");

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };
  /* **************** End Tab Management ******************* */

  /* **************** Web Loader  ******************* */
  if (isLoading || isProductLoading || isAssignVariationLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <ul
                      className="nav nav-pills user-profile-tab"
                      id="pills-tab"
                      role="tablist"
                    >
                      <li className="nav-item" role="presentation">
                        <button
                          className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                            activeTab === "product-basics" ? "active" : ""
                          }`}
                          type="button"
                          onClick={() => handleTabClick("product-basics")}
                        >
                          <i className="ti ti-list me-2 fs-6"></i>
                          <span className="d-none d-md-block">
                            Product Basics
                          </span>
                        </button>
                      </li>
                      <li
                        className="nav-item"
                        role="presentation"
                        style={{ display: detailsTabDisplay }}
                      >
                        <button
                          className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                            activeTab === "product-details" ? "active" : ""
                          }`}
                          type="button"
                          onClick={() => handleTabClick("product-details")}
                        >
                          <i className="ti ti-file-invoice me-2 fs-6"></i>
                          <span className="d-none d-md-block">
                            Product Details
                          </span>
                        </button>
                      </li>
                    </ul>
                    <div className="card-body">
                      <div className="tab-content" id="pills-tabContent">
                        {/* Start Product Basics Tab */}
                        {activeTab === "product-basics" && (
                          <div
                            className="tab-pane fade show active"
                            id="pills-account"
                            role="tabpanel"
                            aria-labelledby="pills-account-tab"
                            tabIndex="0"
                          >
                            <div className="row">
                              <div className="col-lg-12 d-flex align-items-stretch">
                                <div className="card w-100 border position-relative overflow-hidden mb-0">
                                  <div className="card-body p-4">
                                    <h4 className="card-title">
                                      Product Basics
                                    </h4>
                                    <p className="card-subtitle mb-4">
                                      To create Product Basics, add details and
                                      save from here
                                    </p>
                                    <Formik
                                      initialValues={EditProductValues}
                                      validationSchema={validation}
                                      onSubmit={handleSubmit}
                                      enableReinitialize={true} // Ensure form reinitializes when EditProductValues changes
                                    >
                                      <Form
                                        name="product-create"
                                        className="needs-validation"
                                        autoComplete="on"
                                        encType="multipart/form-data"
                                      >
                                        <div className="row">
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="branch_id"
                                                className="form-label"
                                              >
                                                Branch
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="branch_id"
                                                id="branch_id"
                                                className="form-select"
                                                type="select"
                                                options={branchesList}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6"></div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="category_id"
                                                className="form-label"
                                              >
                                                Category
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="category_id"
                                                id="category_id"
                                                className="form-select"
                                                type="select"
                                                options={categoriesList}
                                                onChange={handleCategoryChange}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="sub_category_id"
                                                className="form-label"
                                              >
                                                Sub Category
                                              </label>
                                              <FormikField
                                                name="sub_category_id"
                                                id="sub_category_id"
                                                className="form-select"
                                                type="select"
                                                options={subCategoriesList}
                                                onChange={
                                                  handleSubCategoryChange
                                                }
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="child_category_id"
                                                className="form-label"
                                              >
                                                Child Category
                                              </label>
                                              <FormikField
                                                name="child_category_id"
                                                id="child_category_id"
                                                className="form-select"
                                                type="select"
                                                options={childCategoriesList}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="brand_id"
                                                className="form-label"
                                              >
                                                Brands
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="brand_id"
                                                id="brand_id"
                                                className="form-select"
                                                type="select"
                                                options={brandAry}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="title"
                                                className="form-label"
                                              >
                                                Product Title in English
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="title"
                                                id="title"
                                                placeholder="Product Title in English *"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="title_ar"
                                                className="form-label"
                                              >
                                                Product Title in Arabic
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="title_ar"
                                                id="title_ar"
                                                placeholder="Product Title in Arabic *"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description"
                                                className="form-label"
                                              >
                                                Product Description in English
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description"
                                                id="description"
                                                placeholder="Product Description in English"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description_ar"
                                                className="form-label"
                                              >
                                                Product Description in Arabic
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description_ar"
                                                id="description_ar"
                                                placeholder="Product Description in Arabic"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="note"
                                                className="form-label"
                                              >
                                                Note
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="note"
                                                id="note"
                                                placeholder="Note"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="image"
                                                className="form-label"
                                              >
                                                Product Image
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="file"
                                                name="image"
                                                id="image"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="is_publish"
                                                className="form-label"
                                              >
                                                Product Status
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="is_publish"
                                                id="is_publish"
                                                className="form-select"
                                                type="select"
                                                options={generalStatusList}
                                              />
                                            </div>
                                          </div>
                                          <hr />
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="attribute_id"
                                                className="form-label"
                                              >
                                                Assign Product Variations
                                                <span className="ms-1">
                                                  (Optional)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="attribute_id"
                                                id="attribute_id"
                                                type="checkbox-group"
                                                options={attributesAry}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-12">
                                            <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                              <button
                                                className="btn btn-primary"
                                                type="submit"
                                              >
                                                Update Product
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      </Form>
                                    </Formik>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* End Product Basics Tab */}
                        {/* Start Product Details Tab */}
                        {activeTab === "product-details" && (
                          <div
                            className="tab-pane fade show active"
                            id="pills-account"
                            role="tabpanel"
                            aria-labelledby="pills-account-tab"
                            tabIndex="0"
                          >
                            <div className="row">
                              <div className="col-lg-12 d-flex align-items-stretch">
                                <div className="card w-100 border position-relative overflow-hidden mb-0">
                                  <div className="card-body p-4">
                                    <h4 className="card-title">
                                      Product Details
                                    </h4>
                                    <p className="card-subtitle mb-4">
                                      To update Product Details, add details and save from here
                                    </p>
                                    {/* Add your Product Details Form here */}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* End Product Details Tab */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { useMemo, useState } from "react";
import { Table } from "../../components/datatable";
import { PaginationComponent } from "../../components/pagination";
import { useGetCustomerAttachmentsListQuery, useCreateCustomerAttachmentsMutation, useDeleteCustomerAttachmentsMutation } from "../../feature/api/customerAttachmentsDataApiSlice";
import WebLoader from "../../components/webLoader";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { Modal } from "react-bootstrap";
import { Formik } from "formik";
import * as yup from "yup";
import { Form } from "react-router-dom";
import FormikField from "../../components/formikField";
import { useListAllCustomerQuery } from "../../feature/api/customerDataApiSlice";
import useConfirmDelete from "../../hooks/useConfirmDelete";
import { useGetShopBranchsQuery } from "../../feature/api/branchDataApiSlice";

const initialValues = {
  branch_id: "",
  customer_id: "",
  attachment: "",
};

const validation = yup.object().shape({
  customer_id: yup.string().required().label("Customer"),
  attachment: yup.mixed().required().label("Attachment"),
});

export default function CustomerAttachments() {
  const activePage = "Customer Attachments";
  const linkHref = "/dashboard";

  /* **************** Data Add Model ******************* */
  const [showAddModel, setShowAddModel] = useState(false);
  const handleAddModelClose = () => setShowAddModel(false);
  const handleAddModelShow = () => setShowAddModel(true);
  /* **************** End Data Add Model ******************* */

  /* **************** Start list Customers ******************* */
  const customersData = useListAllCustomerQuery();

  const customersDataList = customersData?.data?.data || [];
  const customersListFilter = customersDataList.map((values) => ({
    value: values.id,
    label: values.customer_name,
  }));
  /* **************** End list Customers ******************* */
  /* **************** Start list all Customer Contact ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBranchId, setSelectedBranchId] = useState("");

  const [filterCustomerId, setFilterCustomerId] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");
  /* **************** Start list all Customer Attachment ******************* */
  const { data: customerAttachmentData, isLoading } = useGetCustomerAttachmentsListQuery({
    page: currentPage,
    customer_id: parseInt(filterCustomerId),
    keywords: filterKeywords,
  });
  const customerAttachmentList = useMemo(() => {
    if (!customerAttachmentData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return customerAttachmentData?.data?.list;
  }, [currentPage, customerAttachmentData?.data.list]);

  const pageData = useMemo(
    () => customerAttachmentData?.data?.page ?? null,
    [customerAttachmentData]
  );

  /* **************** End list all Customer Attachment ******************* */
  /* ****************  Start Filter ****************** */
  const handleCustomerFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterCustomerId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

    /* **************** Start Branch List ******************* */
    const branchListResp = useGetShopBranchsQuery();
    const branchList = branchListResp.data?.data || [];
    const branchesList = branchList.map((values) => ({
      value: values.id,
      label: values.branch_name + ' (' + values.branch_type_name + ')',
    }));
    /* **************** End Branch List ******************* */

    /* **************** Start Fetch Customer Based On Branch for create (Load in Select Box) ******************* */
      
      const { data: customerData } = useListAllCustomerQuery(
        { branch_id: selectedBranchId },
        { skip: selectedBranchId === "" }
      );
      const customerList = useMemo(() => {
        if (!customerData?.data?.length) {
          return [];
        }
        return customerData.data.map((values) => ({
          value: values.id,
          label: values.customer_name,
        }));
      }, [customerData?.data]);
      
      /* **************** Start Handle Branch selection change in Select Box ******************* */
      const handleBranchChange = (e) => {
        const value = e.target.value === "" ? "" : parseInt(e.target.value);
        setSelectedBranchId(value);
      };

    /* **************** End  Handle Branch selection change in Select Box ******************* */


  /* **************** Start Delete Customer Attachments ******************* */

  const { showSimpleConfirm } = useConfirmDelete({
        title: 'Delete Customer Attachments?',
        text: 'Are you sure you want to delete this customer attachments?',
        confirmButtonText: 'Yes, delete customer attachments!',
        cancelButtonText: 'Cancel'
    });

  const [handledeleteDataApi, { isLoading: isDeleteLoading }] =
    useDeleteCustomerAttachmentsMutation();
  const onDeleteDataHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { id: id };
          const resp = await handledeleteDataApi(body).unwrap();
          handleApiSuccess(resp);
        } catch (error) {
          handleApiErrors(error);
        }
      }
  };
  /* **************** End Delete Customer Attachments ******************* */

  /* **************** Start Create Customer Attachments ******************* */
  const [handleCreateDataApi, { isLoading: isCreateLoading }] =
    useCreateCustomerAttachmentsMutation();

  const handleCreateFormSubmitFunction = async (body) => {
      try {
        const formData = new FormData();
        for (const [key, value] of Object.entries(body)) {
          if (key === "attachment" && value) {
            formData.append(key, value);
          } else if (key === 'customer_id') {
            formData.append(key, parseInt(value));
          } else {
            formData.append(key, value);
          }
        }
        const resp = await handleCreateDataApi(formData).unwrap();
        handleApiSuccess(resp);
        handleAddModelClose();
      } catch (error) {
        handleApiErrors(error);
      }
    };
  /* **************** End Create  Customer Attachments ******************* */

  /* **************** Web Loader  ******************* */
  if (isLoading || isDeleteLoading || isCreateLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Customer Attachments List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <button
                          type="button"
                          className="btn btn-primary"
                          onClick={handleAddModelShow}
                        >
                          Create Customer Attachments
                        </button>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterCustomerId}
                          className="form-control search-chat py-2"
                          onChange={handleCustomerFilter}
                        >
                          <option value="">All Customers</option>
                          {customersListFilter.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..." 
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },                      
                      {
                        key: "customer_code",
                        label: "Customer Code",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/viewCustomer/${row.customer_id}`,
                        }),
                      },
                      {
                        key: "customer_name",
                        label: "Customer Name",
                        align: "left",
                      },
                      {
                        key: "file_name",
                        label: "File Name",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                    ]}
                    data={customerAttachmentList}
                    onDeleteHandler={onDeleteDataHandler}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Create Modal --> */}

      <Modal
        show={showAddModel}
        onHide={handleAddModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Customer Attachments </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={initialValues}
            validationSchema={validation}
            onSubmit={handleCreateFormSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
                encType="multipart/form-data"
              >
                <div className="modal-body">
                  <div className="row">
                  <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_id" className="form-label">
                          Branch
                        </label>
                        <FormikField
                          name="branch_id"
                          id="branch_id"
                          className="form-select"
                          type="select"
                          options={branchesList}
                          onChange={handleBranchChange}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="customer_id" className="form-label">
                          Customer
                          <span className="un-validation">(*)</span>
                        </label>
                        <FormikField
                          name="customer_id"
                          id="customer_id"
                          className="form-select"
                          type="select"
                          options={customerList}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label
                          htmlFor="attachment"
                          className="form-label"
                        >
                          Attachment
                          <span className="un-validation">(*)</span>
                        </label>
                        <FormikField
                          type="file"
                          name="attachment"
                          id="attachment"
                          placeholder="Attachment *"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start"
                    data-bs-dismiss="modal"
                    onClick={handleAddModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Create Customer Attachments
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>

      {/* <!-- Create Modal end --> */}
      <CommonFooter />
    </>
  );
}

import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { Table } from "../../../components/datatable";
import { PaginationComponent } from "../../../components/pagination";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import { useEditCustomerMutation, useSingleCustomerQuery } from "../../../feature/api/customerDataApiSlice";
import { useNavigate, useParams } from "react-router-dom";
import { Modal } from "react-bootstrap";
import WebLoader from "../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import {
  useCreateCustomerContactsMutation,
  useDeleteCustomerContactsMutation,
  useEditCustomerContactsMutation,
  useGetCustomerContactsListQuery,
} from "../../../feature/api/customerContactsDataApiSlice";
import useConfirmDelete from "../../../hooks/useConfirmDelete";
import {
  useCreateCustomerNotesMutation,
  useDeleteCustomerNotesMutation,
  useEditCustomerNotesMutation,
  useGetCustomerNotesListQuery,
} from "../../../feature/api/customerNotesDataApiSlice";
import {
  useCreateCustomerAttachmentsMutation,
  useDeleteCustomerAttachmentsMutation,
  useGetCustomerAttachmentsListQuery,
} from "../../../feature/api/customerAttachmentsDataApiSlice";
import { useSelector } from "react-redux";
import { handleCustomError } from "../../../hooks/handleCustomError";

const validation = yup.object().shape({
  customer_name: yup.string().required().label("Customer Name"),
  phone_code: yup.string().required().label("Phone Code"),
  phone: yup.string().required().label("Phone"),
  email: yup.string().email().label("Email"),
  qid: yup.string().length(11).required().label("Qatar ID"),
  billing_address: yup.string().label("Billing Address"),
  shipping_address: yup.string().label("Shipping Address"),
  tax_id: yup.string().label("Tax ID"),
  payment_terms: yup.string().label("Payment Terms"),
  credit_limit: yup.string().label("Credit Limit"),
  status: yup.string().required().label("Status"),
  remarks: yup.string().label("Remarks"),
  branch_id: yup.string().required().label("Branch"),
});
export default function ViewCustomer() {
  const navigation = useNavigate();
  const { id } = useParams();
  const customerId = parseInt(id);
  const activePage = "Customers Master";
  const linkHref = "/dashboard";

  /* **************** Start fetching single customer data ******************* */
  let { data: singleCustomer, isLoading: isCustomerLoading } = useSingleCustomerQuery({
      customer_id: customerId,
  });

  const customerData = useMemo(() => {
    return singleCustomer?.data || null;
  }, [singleCustomer]);
  /* **************** Start fetching single customer data ******************* */

    const [initialValues, setFormValues] = useState({
    customer_name: customerData?.customer_name || null,
    phone_code: customerData?.phone_code || "",
    phone: customerData?.phone || "",
    email: customerData?.email || "",
    qid: customerData?.qid || "",
    billing_address: customerData?.billing_address || "",
    shipping_address: customerData?.shipping_address || "",
    tax_id: customerData?.tax_id || "",
    payment_terms: customerData?.payment_terms || "",
    credit_limit: customerData?.credit_limit || "",
    status: customerData?.status || "",
    remarks: customerData?.remarks || "",
    branch_id: customerData?.branch_id || "",
    customer_code: customerData?.customer_code || "",
  });

  useEffect(() => {
    if (isCustomerLoading) return;

    if (!customerData) {
      handleCustomError("Error was found, please try again later!");
        navigation("/customers");
      } else {
        setFormValues({
          customer_name: customerData?.customer_name || null,
          phone_code: customerData?.phone_code || "",
          phone: customerData?.phone || "",
          email: customerData?.email || "",
          qid: customerData?.qid || "",
          billing_address: customerData?.billing_address || "",
          shipping_address: customerData?.shipping_address || "",
          tax_id: customerData?.tax_id || "",
          payment_terms: customerData?.payment_terms || "",
          credit_limit: customerData?.credit_limit || "",
          status: customerData?.status || "",
          remarks: customerData?.remarks || "",
          branch_id: customerData?.branch_id || "",
          customer_code: customerData?.customer_code || "",
        });
      }
    }, [isCustomerLoading, customerData, navigation]);

  /* **************** Start list all countries ******************* */

  const countries = useSelector((state) => state.commonState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + " (" + values.phone_code + ")",
      }))
    : [];
  /* **************** End list all countries ******************* */

  /* **************** Start list User Status ******************* */
  const userStatusData = useSelector((state) => state.commonState.userStatus);
  const userStatusDataList = userStatusData?.data || [];
  const userStatusList = userStatusDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list User Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Edit Customer ******************* */
  const [handleEditCustomerApi, { isLoading: isEditCustomerLoading }] =
    useEditCustomerMutation();
  const handleSubmit = async (body) => {
    try {
      const createBody = {
        ...body,
        customer_id: parseInt(customerId),
        branch_id: parseInt(body.branch_id),
        status: parseInt(body.status),
      };
      const resp = await handleEditCustomerApi(createBody).unwrap();
      setFormValues({ ...body });
      handleApiSuccess(resp);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Customer ******************* */

  /* **************** Start Customer Contacts Section ************** */

  const contactValidation = yup.object().shape({
    contact_name: yup.string().required().label("Contact Name"),
    phone_code: yup.string().required().label("Phone Code"),
    phone: yup.string().required().label("Phone"),
    email: yup.string().email().label("Email"),
    remarks: yup.string().label("Remarks"),
  });

  /* **************** Start Contact Data Add Modal ******************* */
  const [showAddContactModal, setShowAddContactModal] = useState(false);
  const handleAddContactModalClose = () => setShowAddContactModal(false);
  const handleAddContactModalShow = () => setShowAddContactModal(true);
  /* **************** End Start Contact Data Add Modal ******************* */

  /* **************** Start Contact Data Edit Modal ******************* */
  const [showEditContactModal, setShowEditContactModal] = useState(false);
  const handleContactEditModalClose = () => setShowEditContactModal(false);
  const handleContactEditModalShow = () => setShowEditContactModal(true);
  /* **************** End Contact Data Edit Modal ******************* */

  /* **************** Start list all Customer Contact ******************* */

  const [currentContactPage, setCurrentContactPage] = useState(1);
  const [filterContactsTableKeywords, setFilterContactKeywords] = useState("");

  /* **************** Start list all Customer Contacts ******************* */

  const { data: customerContactsData, isLoading: isLoadingContacts } =
    useGetCustomerContactsListQuery({
      page: currentContactPage,
      customer_id: parseInt(customerId),
      keywords: filterContactsTableKeywords,
    });
  const customerContactsList = useMemo(() => {
    if (!customerContactsData?.data.list?.length) {
      if (currentContactPage > 1)
        setCurrentContactPage((current) => current - 1);
      return [];
    }
    return customerContactsData?.data?.list;
  }, [currentContactPage, customerContactsData?.data.list]);

  const contactPageData = useMemo(
    () => customerContactsData?.data?.page ?? null,
    [customerContactsData]
  );

  /* **************** End list all Customer Contacts ******************* */

  /* **************** Start Filter ***************** */
  const handleContactKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterContactKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchContactData = async (page) => {
    try {
      setCurrentContactPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start Delete Customer Contact ******************* */

  const { showSimpleConfirm: showSimpleContactConfirm } = useConfirmDelete({
    title: "Delete Customer Contact?",
    text: "Are you sure you want to delete this customer contact?",
    confirmButtonText: "Yes, delete customer contact!",
    cancelButtonText: "Cancel",
  });

  const [handleDeleteContactDataApi, { isLoading: isDeleteContactLoading }] =
    useDeleteCustomerContactsMutation();
  const onDeleteContactDataHandler = async (id) => {
    const confirmed = await showSimpleContactConfirm();
    if (confirmed) {
      try {
        const body = { contact_id: id };
        const resp = await handleDeleteContactDataApi(body).unwrap();
        handleApiSuccess(resp);
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };

  /* **************** End Delete Customer Contact ******************* */

  /* **************** Start Create Customer Contact ******************* */
  const [handleCreateContactDataApi, { isLoading: isCreateContactLoading }] =
    useCreateCustomerContactsMutation();
  const handleCreateContactFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        customer_id: parseInt(customerId),
      };
      const resp = await handleCreateContactDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddContactModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Customer Contact ******************* */

  const [editContactValues, setEditContactValues] = useState({
    customer_id: "",
    contact_name: "",
    phone_code: "",
    phone: "",
    email: "",
    remarks: "",
  });
  const handleOpenModal = (values) => {
    setEditContactValues(values);
    handleContactEditModalShow();
  };

  const onEditContactDataDetailsHandler = (d) => {
    handleOpenModal({
      customer_id: d?.customer_id ? parseInt(d.customer_id) : "",
      id: d?.id || "",
      contact_name: d?.contact_name || "",
      phone_code: d?.phone_code || "",
      phone: d?.phone || "",
      email: d?.email || "",
      remarks: d?.remarks || "",
    });
  };

  const [handleEditContactDataApi, { isLoading: isEditContactLoading }] =
    useEditCustomerContactsMutation();
  const contactDataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        contact_id: parseInt(body.id),
        customer_id: parseInt(body.customer_id),
      };
      const resp = await handleEditContactDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleContactEditModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* *************** End Customer Contacts Section **************** */

  /* *************** Start Customer Notes Section **************** */

  const notesValidation = yup.object().shape({
    remarks: yup.string().required().label("Remarks"),
  });

  /* **************** Start Notes Data Add Modal ******************* */
  const [showAddNotesModal, setShowAddNotesModal] = useState(false);
  const handleAddNotesModalClose = () => setShowAddNotesModal(false);
  const handleAddNotesModalShow = () => setShowAddNotesModal(true);
  /* **************** End Notes Data Add Modal ******************* */

  /* **************** Notes Data Edit Modal ******************* */
  const [showEditNotesModal, setShowEditNotesModal] = useState(false);
  const handleEditNotesModalClose = () => setShowEditNotesModal(false);
  const handleEditNotesModalShow = () => setShowEditNotesModal(true);
  /* **************** End Notes Data Edit Modal ******************* */

  const [currentNotesPage, setCurrentNotesPage] = useState(1);

  const [filterNotesKeywords, setFilterNotesKeywords] = useState("");
  /* **************** Start list all Customer Notes ******************* */
  const { data: customerNotesData, isLoading: isNotesLoading } =
    useGetCustomerNotesListQuery({
      page: currentNotesPage,
      customer_id: parseInt(customerId),
      keywords: filterNotesKeywords,
    });
  const customerNotesList = useMemo(() => {
    if (!customerNotesData?.data.list?.length) {
      if (currentNotesPage > 1) setCurrentNotesPage((current) => current - 1);
      return [];
    }
    return customerNotesData?.data?.list;
  }, [currentNotesPage, customerNotesData?.data.list]);

  const notesPageData = useMemo(
    () => customerNotesData?.data?.page ?? null,
    [customerNotesData]
  );

  /* ****************  Start Filter ****************** */
  const handleNotesKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterNotesKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchNotesData = async (page) => {
    try {
      setCurrentNotesPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start Delete Customer Notes ******************* */

  const { showSimpleConfirm: showSimpleConfirmNotes } = useConfirmDelete({
    title: "Delete Customer Notes?",
    text: "Are you sure you want to delete this customer notes?",
    confirmButtonText: "Yes, delete customer notes!",
    cancelButtonText: "Cancel",
  });

  const [handleDeleteNotesDataApi, { isLoading: isDeleteNotesLoading }] =
    useDeleteCustomerNotesMutation();
  const onDeleteNotesDataHandler = async (id) => {
    const confirmed = await showSimpleConfirmNotes();
    if (confirmed) {
      try {
        const body = { note_id: id };
        const resp = await handleDeleteNotesDataApi(body).unwrap();
        handleApiSuccess(resp);
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };
  /* **************** End Delete Customer Notes ******************* */

  /* **************** Start Create Customer Notes ******************* */
  const [handleCreateNotesDataApi, { isLoading: isCreateNotesLoading }] =
    useCreateCustomerNotesMutation();
  const handleCreateNotesFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        customer_id: parseInt(customerId),
      };
      const resp = await handleCreateNotesDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddNotesModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Customer Notes ******************* */

  const notesInitialValues = {
    customer_id: customerId,
    remarks: "",
  };

  const [editNotesValues, setEditNotesValues] = useState({
    customer_id: "",
    remarks: "",
  });

  const handleOpenNotesModal = (values) => {
    setEditNotesValues(values);
    handleEditNotesModalShow();
  };
  const onEditNotesDataDetailsHandler = (d) => {
    handleOpenNotesModal({
      customer_id: d?.customer_id ? parseInt(d.customer_id) : "",
      id: d?.id || "",
      remarks: d?.note_text || "",
    });
  };

  const [handleEditNotesDataApi, { isLoading: isEditNotesLoading }] =
    useEditCustomerNotesMutation();
  const notesDataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        note_id: parseInt(body.id),
        customer_id: parseInt(body.customer_id),
      };
      const resp = await handleEditNotesDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleEditNotesModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* *************** End Customer Notes Section **************** */

  /* *************** Start Customer Attachment Section **************** */

  const attachmentInitialValues = {
    customer_id: customerId,
    attachment: "",
  };

  const attachmentValidation = yup.object().shape({
    attachment: yup.mixed().required().label("Attachment"),
  });

  /* **************** Data Add Modal ******************* */
  const [showAddAttachmentModal, setShowAddAttachmentModal] = useState(false);
  const handleAddAttachmentModalClose = () => setShowAddAttachmentModal(false);
  const handleAddAttachmentModalShow = () => setShowAddAttachmentModal(true);
  /* **************** End Data Add Modal ******************* */

  /* **************** Start list all Customer Contact ******************* */
  const [currentAttachmentPage, setCurrentAttachmentPage] = useState(1);
  const [filterAttachmentKeywords, setFilterAttachmentKeywords] = useState("");
  /* **************** Start list all Customer Attachment ******************* */
  const { data: customerAttachmentData, isAttachmentsLoading } =
    useGetCustomerAttachmentsListQuery({
      page: currentAttachmentPage,
      customer_id: parseInt(customerId),
      keywords: filterAttachmentKeywords,
    });
  const customerAttachmentList = useMemo(() => {
    if (!customerAttachmentData?.data.list?.length) {
      if (currentAttachmentPage > 1)
        setCurrentAttachmentPage((current) => current - 1);
      return [];
    }
    return customerAttachmentData?.data?.list;
  }, [currentAttachmentPage, customerAttachmentData?.data.list]);

  const attachmentPageData = useMemo(
    () => customerAttachmentData?.data?.page ?? null,
    [customerAttachmentData]
  );

  /* **************** End list all Customer Attachment ******************* */

  /* ****************  Start Filter ****************** */
  const handleAttachmentKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterAttachmentKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchAttachmentData = async (page) => {
    try {
      setCurrentAttachmentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start Create Customer Attachments ******************* */
  const [
    handleCreateAttachmentDataApi,
    { isLoading: isCreateAttachmentLoading },
  ] = useCreateCustomerAttachmentsMutation();

  const handleCreateAttachmentFormSubmitFunction = async (body) => {
    try {
      const formData = new FormData();
      for (const [key, value] of Object.entries(body)) {
        if (key === "attachment" && value) {
          formData.append(key, value);
        } else if (key === "customer_id") {
          formData.append(key, parseInt(customerId));
        } else {
          formData.append(key, value);
        }
      }
      const resp = await handleCreateAttachmentDataApi(formData).unwrap();
      handleApiSuccess(resp);
      handleAddAttachmentModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Customer Attachments ******************* */

  /* **************** Start Delete Customer Attachments ******************* */

  const { showSimpleConfirm: showSimpleAttachmentConfirm } = useConfirmDelete({
    title: "Delete Customer Attachment?",
    text: "Are you sure you want to delete this customer attachment?",
    confirmButtonText: "Yes, delete customer attachment!",
    cancelButtonText: "Cancel",
  });

  const [
    handleDeleteAttachmentDataApi,
    { isLoading: isDeleteAttachmentLoading },
  ] = useDeleteCustomerAttachmentsMutation();
  const onDeleteAttachmentDataHandler = async (id) => {
    const confirmed = await showSimpleAttachmentConfirm();
    if (confirmed) {
      try {
        const body = { id: id };
        const resp = await handleDeleteAttachmentDataApi(body).unwrap();
        handleApiSuccess(resp);
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };
  /* **************** End Delete Customer Attachments ******************* */

  /* *************** End Customer Attachment Section **************** */

  /* **************** Tab Management ******************* */
  const [activeTab, setActiveTab] = useState("customer-details");

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };
  /* **************** End Tab Management ******************* */

  /* **************** Web Loader  ******************* */
  if (
    isEditCustomerLoading ||
    isLoadingContacts ||
    isEditContactLoading ||
    isDeleteContactLoading ||
    isEditNotesLoading ||
    isDeleteNotesLoading ||
    isNotesLoading ||
    isDeleteAttachmentLoading ||
    isAttachmentsLoading ||
    isCreateContactLoading ||
    isCreateNotesLoading ||
    isCreateAttachmentLoading || 
    isCustomerLoading
  )
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  ></div>
                  <ul
                    className="nav nav-pills user-profile-tab"
                    id="pills-tab"
                    role="tablist"
                  >
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "customer-details" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("customer-details")}
                      >
                        <i className="ti ti-user-circle me-2 fs-6"></i>
                        <span className="d-none d-md-block">
                          Customer Details
                        </span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "contacts" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("contacts")}
                      >
                        <i className="ti ti-phone me-2 fs-6"></i>
                        <span className="d-none d-md-block">Contacts</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "notes" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("notes")}
                      >
                        <i className="ti ti-note me-2 fs-6"></i>
                        <span className="d-none d-md-block">Notes</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "attachments" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("attachments")}
                      >
                        <i className="ti ti-paperclip me-2 fs-6"></i>
                        <span className="d-none d-md-block">Attachments</span>
                      </button>
                    </li>
                  </ul>
                  <div className="card-body">
                    <div className="tab-content" id="pills-tabContent">
                      {/* Start Customer Details Tab */}
                      {activeTab === "customer-details" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-account"
                          role="tabpanel"
                          aria-labelledby="pills-account-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="card-body p-4">
                                  <h4 className="card-title">
                                    Customer Details
                                  </h4>
                                  <p className="card-subtitle mb-4">
                                    To update Customer, add details and save
                                    from here
                                  </p>
                                  <Formik
                                    initialValues={initialValues}
                                    validationSchema={validation}
                                    onSubmit={handleSubmit}
                                    enableReinitialize={true}
                                  >
                                    <Form
                                      name="customer-edit"
                                      className="needs-validation"
                                      autoComplete="off"
                                      encType="multipart/form-data"
                                    >
                                      <div className="row">
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="branch_id"
                                              className="form-label"
                                            >
                                              Branch
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="branch_id"
                                              id="branch_id"
                                              className="form-select"
                                              type="select"
                                              options={branchesList}
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6"></div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="customer_code"
                                              className="form-label"
                                            >
                                              Customer Code
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="customer_code"
                                              id="customer_code"
                                              placeholder="Customer Code *"
                                              autoComplete="off"
                                              disabled
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="customer_name"
                                              className="form-label"
                                            >
                                              Customer Name
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="customer_name"
                                              id="customer_name"
                                              placeholder="Customer Name *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              className="form-label"
                                              htmlFor="phone"
                                            >
                                              Phone Number{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <div className="row">
                                              <div className="col-4">
                                                <FormikField
                                                  name="phone_code"
                                                  id="phone_code"
                                                  className="form-select"
                                                  type="select"
                                                  options={countriesList}
                                                />
                                              </div>
                                              <div className="col-8">
                                                <FormikField
                                                  type="number"
                                                  name="phone"
                                                  id="phone"
                                                  placeholder="Phone Number *"
                                                  autoComplete="off"
                                                  className="form-control"
                                                />
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="email"
                                              className="form-label"
                                            >
                                              Email
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="email"
                                              id="email"
                                              placeholder="Email"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="qid"
                                              className="form-label"
                                            >
                                              Qatar ID{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              type="number"
                                              name="qid"
                                              id="qid"
                                              placeholder=" Qatar ID *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6"></div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="billing_address"
                                              className="form-label"
                                            >
                                              Billing Address
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="billing_address"
                                              id="billing_address"
                                              placeholder="Billing Address"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="shipping_address"
                                              className="form-label"
                                            >
                                              Shipping Address
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="shipping_address"
                                              id="shipping_address"
                                              placeholder="Shipping Address"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="payment_terms"
                                              className="form-label"
                                            >
                                              Payment Terms
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="payment_terms"
                                              id="payment_terms"
                                              placeholder="Payment Terms"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="credit_limit"
                                              className="form-label"
                                            >
                                              Credit Limit
                                            </label>
                                            <FormikField
                                              type="number"
                                              name="credit_limit"
                                              id="credit_limit"
                                              placeholder="Credit Limit"
                                              autoComplete="off"
                                              className="form-control"
                                              step="0.01"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="tax_id"
                                              className="form-label"
                                            >
                                              Tax ID
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="tax_id"
                                              id="tax_id"
                                              placeholder="Tax ID"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="status"
                                              className="form-label"
                                            >
                                              Status
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="status"
                                              id="status"
                                              className="form-select"
                                              type="select"
                                              options={userStatusList}
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="remarks"
                                              className="form-label"
                                            >
                                              Remarks
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="remarks"
                                              id="remarks"
                                              placeholder="Remarks"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-12">
                                          <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                            <button
                                              className="btn btn-primary"
                                              type="submit"
                                            >
                                              Edit Customer
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </Form>
                                  </Formik>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      {/* End Customer Details Tab */}

                      {/* Start Customer Contacts Tab */}

                      {activeTab === "contacts" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-contacts"
                          role="tabpanel"
                          aria-labelledby="pills-contacts-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Customer Contact List
                                    </h4>

                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li
                                          className="breadcrumb-item"
                                          aria-current="page"
                                        >
                                          <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={handleAddContactModalShow}
                                          >
                                            Create Customer Contact
                                          </button>
                                        </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex  gap-6"></div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={handleContactKeywordsFilter}
                                          placeholder="Keyword Search..."
                                          value={filterContactsTableKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        {
                                          key: "sel_id",
                                          label: "#",
                                          align: "left",
                                        },
                                        {
                                          key: "contact_name",
                                          label: "Contact Name",
                                          align: "left",
                                        },
                                        {
                                          key: "phone",
                                          label: "Phone",
                                          align: "left",
                                        },
                                        {
                                          key: "email",
                                          label: "Email",
                                          align: "left",
                                        },
                                        {
                                          key: "created_at",
                                          label: "Created At",
                                          align: "left",
                                        },
                                      ]}
                                      data={customerContactsList}
                                      onDeleteHandler={
                                        onDeleteContactDataHandler
                                      }
                                      onEditHandler={
                                        onEditContactDataDetailsHandler
                                      }
                                    />
                                    <PaginationComponent
                                      totalCount={contactPageData?.total_count}
                                      pageSize={contactPageData?.page_size}
                                      currentPage={currentContactPage}
                                      setCurrentPage={setCurrentContactPage}
                                      onPageChange={fetchContactData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Start Customer Contacts Create Modal */}

                      {/* <!-- Create Modal --> */}

                      <Modal
                        show={showAddContactModal}
                        onHide={handleAddContactModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title>Create Customer Contact </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={editContactValues}
                            validationSchema={contactValidation}
                            onSubmit={handleCreateContactFormSubmitFunction}
                          >
                            {({ handleSubmit }) => (
                              <Form
                                name="role-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="modal-body">
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="contact_name"
                                          className="form-label"
                                        >
                                          Contact Name
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="contact_name"
                                          id="contact_name"
                                          placeholder="Contact Name *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label className="form-label">
                                          Phone Number{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <div className="row">
                                          <div className="col-4">
                                            <FormikField
                                              name="phone_code"
                                              id="phone_code"
                                              className="form-select"
                                              type="select"
                                              options={countriesList}
                                            />
                                          </div>
                                          <div className="col-8">
                                            <FormikField
                                              type="number"
                                              name="phone"
                                              id="phone"
                                              placeholder="Phone Number *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="email"
                                          className="form-label"
                                        >
                                          Email
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="email"
                                          id="email"
                                          placeholder="Email"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="remarks"
                                          className="form-label"
                                        >
                                          Remarks
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="remarks"
                                          id="remarks"
                                          placeholder="Remarks"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="modal-footer">
                                  <button
                                    type="button"
                                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                                    data-bs-dismiss="modal"
                                    onClick={handleAddContactModalClose}
                                  >
                                    Close
                                  </button>
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                    onClick={handleSubmit}
                                  >
                                    Create Customer Contact
                                  </button>
                                </div>
                              </Form>
                            )}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* <!-- Create Modal end --> */}

                      {/* End Customer Contact Create Modal */}

                      {/* Start Customer Contacts Edit Modal */}

                      <Modal
                        show={showEditContactModal}
                        onHide={handleContactEditModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title> Update Customer Contact</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={editContactValues}
                            enableReinitialize
                            validationSchema={contactValidation}
                            onSubmit={contactDataUpdateFormSubmit}
                          >
                            {({ handleSubmit }) => {
                              return (
                                <Form
                                  name="role-update"
                                  className="needs-validation"
                                  autoComplete="off"
                                >
                                  <FormikField
                                    type="hidden"
                                    name="id"
                                    id="id"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                  <div className="modal-body">
                                    <div className="row">
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="contact_name"
                                            className="form-label"
                                          >
                                            Contact Name
                                            <span className="un-validation">
                                              (*)
                                            </span>
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="contact_name"
                                            id="contact_name"
                                            placeholder="Contact Name"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="phone"
                                            className="form-label"
                                          >
                                            Phone Number{" "}
                                            <span className="un-validation">
                                              (*)
                                            </span>{" "}
                                            :
                                          </label>
                                          <div className="row">
                                            <div className="col-4">
                                              <FormikField
                                                name="phone_code"
                                                id="phone_code"
                                                className="form-select"
                                                type="select"
                                                options={countriesList}
                                              />
                                            </div>
                                            <div className="col-8">
                                              <FormikField
                                                type="number"
                                                name="phone"
                                                id="phone"
                                                placeholder="Phone Number"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="email"
                                            className="form-label"
                                          >
                                            Email
                                          </label>
                                          <FormikField
                                            type="email"
                                            name="email"
                                            id="email"
                                            placeholder="Email"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="remarks"
                                            className="form-label"
                                          >
                                            Remarks
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="remarks"
                                            id="remarks"
                                            placeholder="Remarks"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="modal-footer">
                                    <button
                                      type="button"
                                      className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                                      data-bs-dismiss="modal"
                                      onClick={handleContactEditModalClose}
                                    >
                                      Close
                                    </button>
                                    <button
                                      className="btn btn-primary"
                                      onClick={handleSubmit}
                                    >
                                      Update Customer Contact
                                    </button>
                                  </div>
                                </Form>
                              );
                            }}
                          </Formik>
                        </Modal.Body>
                        {/* <Modal.Footer></Modal.Footer> */}
                      </Modal>

                      {/* End Customer Contacts Edit Modal */}

                      {/* End Customer Contacts Tab */}

                      {/* Start Notes Tab */}

                      {activeTab === "notes" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-notes"
                          role="tabpanel"
                          aria-labelledby="pills-notes-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Customer Notes List
                                    </h4>
                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li
                                          className="breadcrumb-item"
                                          aria-current="page"
                                        >
                                          <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={handleAddNotesModalShow}
                                          >
                                            Create Customer Notes
                                          </button>
                                        </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex  gap-6"></div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={handleNotesKeywordsFilter}
                                          placeholder="Keyword Search..."
                                          value={filterNotesKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        {
                                          key: "sel_id",
                                          label: "#",
                                          align: "left",
                                        },
                                        {
                                          key: "note_text",
                                          label: "Note",
                                          align: "left",
                                        },
                                        {
                                          key: "created_at",
                                          label: "Created At",
                                          align: "left",
                                        },
                                      ]}
                                      data={customerNotesList}
                                      onDeleteHandler={onDeleteNotesDataHandler}
                                      onEditHandler={
                                        onEditNotesDataDetailsHandler
                                      }
                                    />
                                    <PaginationComponent
                                      totalCount={notesPageData?.total_count}
                                      pageSize={notesPageData?.page_size}
                                      currentPage={currentNotesPage}
                                      setCurrentPage={setCurrentNotesPage}
                                      onPageChange={fetchNotesData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Start Notes Create Modal  */}

                      <Modal
                        show={showAddNotesModal}
                        onHide={handleAddNotesModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title>Create Customer Notes </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={notesInitialValues}
                            validationSchema={notesValidation}
                            onSubmit={handleCreateNotesFormSubmitFunction}
                          >
                            {({ handleSubmit }) => (
                              <Form
                                name="role-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="modal-body">
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="remarks"
                                          className="form-label"
                                        >
                                          Remarks
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="remarks"
                                          id="remarks"
                                          placeholder="Remarks *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="modal-footer">
                                  <button
                                    type="button"
                                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                                    data-bs-dismiss="modal"
                                    onClick={handleAddNotesModalClose}
                                  >
                                    Close
                                  </button>
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                    onClick={handleSubmit}
                                  >
                                    Create Customer Notes
                                  </button>
                                </div>
                              </Form>
                            )}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* End Notes Create Modal */}

                      {/* <!-- Start Notes Edit Modal --> */}

                      <Modal
                        show={showEditNotesModal}
                        onHide={handleEditNotesModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title> Update Customer Notes</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={editNotesValues}
                            enableReinitialize
                            validationSchema={notesValidation}
                            onSubmit={notesDataUpdateFormSubmit}
                          >
                            {({ handleSubmit }) => {
                              return (
                                <Form
                                  name="role-update"
                                  className="needs-validation"
                                  autoComplete="off"
                                >
                                  <FormikField
                                    type="hidden"
                                    name="id"
                                    id="id"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                  <div className="modal-body">
                                    <div className="row">
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="remarks"
                                            className="form-label"
                                          >
                                            Remarks
                                            <span className="un-validation">
                                              (*)
                                            </span>
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="remarks"
                                            id="remarks"
                                            placeholder="Remarks *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="modal-footer">
                                    <button
                                      type="button"
                                      className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                                      data-bs-dismiss="modal"
                                      onClick={handleEditNotesModalClose}
                                    >
                                      Close
                                    </button>
                                    <button
                                      className="btn btn-primary"
                                      onClick={handleSubmit}
                                    >
                                      Update Customer Notes
                                    </button>
                                  </div>
                                </Form>
                              );
                            }}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* <!-- End Notes Edit Modal end --> */}

                      {/* End Notes Tab */}

                      {/* Start Attachments Tab */}

                      {activeTab === "attachments" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-attachments"
                          role="tabpanel"
                          aria-labelledby="pills-attachments-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Customer Attachments List
                                    </h4>
                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li
                                          className="breadcrumb-item"
                                          aria-current="page"
                                        >
                                          <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={
                                              handleAddAttachmentModalShow
                                            }
                                          >
                                            Create Customer Attachments
                                          </button>
                                        </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex gap-6"></div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={
                                            handleAttachmentKeywordsFilter
                                          }
                                          placeholder="Keyword Search..."
                                          value={filterAttachmentKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        {
                                          key: "sel_id",
                                          label: "#",
                                          align: "left",
                                        },
                                        {
                                          key: "file_name",
                                          label: "File Name",
                                          align: "left",
                                        },
                                        {
                                          key: "created_at",
                                          label: "Created At",
                                          align: "left",
                                        },
                                      ]}
                                      data={customerAttachmentList}
                                      onDeleteHandler={
                                        onDeleteAttachmentDataHandler
                                      }
                                    />
                                    <PaginationComponent
                                      totalCount={
                                        attachmentPageData?.total_count
                                      }
                                      pageSize={attachmentPageData?.page_size}
                                      currentPage={currentAttachmentPage}
                                      setCurrentPage={setCurrentAttachmentPage}
                                      onPageChange={fetchAttachmentData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* <!-- Start Create Attachment Modal --> */}

                      <Modal
                        show={showAddAttachmentModal}
                        onHide={handleAddAttachmentModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title>
                            Create Customer Attachments{" "}
                          </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={attachmentInitialValues}
                            validationSchema={attachmentValidation}
                            onSubmit={handleCreateAttachmentFormSubmitFunction}
                          >
                            {({ handleSubmit }) => (
                              <Form
                                name="role-create"
                                className="needs-validation"
                                autoComplete="off"
                                encType="multipart/form-data"
                              >
                                <div className="modal-body">
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="attachment"
                                          className="form-label"
                                        >
                                          Attachment
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="file"
                                          name="attachment"
                                          id="attachment"
                                          placeholder="Attachment *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="modal-footer">
                                  <button
                                    type="button"
                                    className="btn bg-danger-subtle text-danger  waves-effect text-start"
                                    data-bs-dismiss="modal"
                                    onClick={handleAddAttachmentModalClose}
                                  >
                                    Close
                                  </button>
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                    onClick={handleSubmit}
                                  >
                                    Create Customer Attachments
                                  </button>
                                </div>
                              </Form>
                            )}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* <!-- End Create Attachment Modal --> */}

                      {/* End Attachments Tab */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}

import CommonHeader from "../../components/layout/common_header";
import TopBar from "../../components/layout/topBar";
import {
  useDeleteStaffMutation,
  useGetAllStaffsQuery,
} from "../../feature/api/staffsDataApiSlice";
import WebLoader from "../../components/webLoader";
import { useMemo, useState } from "react";
import { PaginationComponent } from "../../components/pagination";
import { Table } from "../../components/datatable";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import Breadcrumb from "../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import CommonFooter from "../../components/layout/common_footer";
import useConfirmDelete from "../../hooks/useConfirmDelete";
import { useSelector } from "react-redux";
import { useGetShopBranchsQuery } from "../../feature/api/branchDataApiSlice";

export default function StaffList() {
  const navigation = useNavigate();

  const activePage = "Staff Master";
  const linkHref = "/dashboard";

  /* **************** Start list all Staff ******************* */

  const [currentPage, setCurrentPage] = useState(1);
  const [filterKeywords, setFilterKeywords] = useState("");
  const [filterStatus, setFilterStatus] = useState(null);
  const [filterBranchId, setFilterBranchId] = useState(null);
  const { data: staffsData, isLoading } = useGetAllStaffsQuery({
    page: currentPage,
    keywords: filterKeywords,
    status: parseInt(filterStatus),
    branch_id: parseInt(filterBranchId)
  });

  const staffList = useMemo(() => {
    if (!staffsData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return staffsData?.data?.list;
  }, [currentPage, staffsData?.data.list]);

  const pageData = useMemo(() => staffsData?.data?.page ?? null, [staffsData]);

   /* **************** Start list User Status ******************* */
    const userStatusData = useSelector((state) => state.commonState.userStatus);
    const userStatusDataList = userStatusData?.data || [];
    const userStatusList = userStatusDataList.map((values) => ({
      value: values.id,
      label: values.status,
    }));
    /* **************** End list User Status ******************* */
  
    /* **************** Start Branch List ******************* */
    const branchListResp = useGetShopBranchsQuery();
    const branchList = branchListResp.data?.data || [];
    const branchesList = branchList.map((values) => ({
      value: values.id,
      label: values.branch_name + ' (' + values.branch_type_name + ')',
    }));
    /* **************** End Branch List ******************* */

/* ****************  Start Filter ****************** */    

 const handleStatusFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterStatus(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleBranchFilter = (event) => {
        try {
          const selectedValue = event.target.value;
          setFilterBranchId(selectedValue);
        } catch (error) {
          handleApiErrors(error);
        }
    };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */

  /* **************** Fetch data when page changes ******************* */
  const fetchData = async (page) => {
    console.log(`Fetching data for page ${page}`);
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End list all Staff ******************* */

  /* **************** Start Delete Staff ******************* */
  const { showSimpleConfirm } = useConfirmDelete({
            title: 'Delete Staff?',
            text: 'Are you sure you want to delete this staff?',
            confirmButtonText: 'Yes, delete staff!',
            cancelButtonText: 'Cancel'
          });
  const [handledeleteStaffApi, { isLoading: isDeleteLoading }] =
    useDeleteStaffMutation();
  const onDeleteStaffHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { staff_id: id };
          const resp = await handledeleteStaffApi(body).unwrap();
          handleApiSuccess(resp);
        } catch (error) {
          handleApiErrors(error);
        }
    }
  };
  /* **************** End Delete Staff ******************* */

  /* **************** Start Edit Staff ******************* */
  const onEditStaffDetailsHandler = (d) => {
    navigation(`/editStaff/${d.id}`);
  };

  /* **************** End Edit Staff ******************* */

  /* **************** Web Loader  ******************* */
  if (isLoading || isDeleteLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Staff List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          className="btn btn-primary"
                          to={"/createStaff"}
                        >
                          Add Staff
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                <div className="d-flex gap-6">
                      <div>
                        <select
                          value={filterBranchId}
                          className="form-control search-chat py-2"
                          onChange={handleBranchFilter}
                        >
                          <option value="">All Branches</option>
                          {branchesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2"
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {userStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "branch_name",
                        label: "Branch Name",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/editBranch/${row.branch_id}`,
                        }),
                      },
                      {
                        key: "role_name",
                        label: "Role",
                        align: "left",
                      },
                      {
                        key: "name",
                        label: "Name",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/editStaff/${row.id}`,
                        }),
                      },
                      {
                        key: "email",
                        label: "Email",
                        align: "left",
                      },
                      {
                        key: "phone",
                        label: "Phone Number",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                      {
                        key: "updated_at",
                        label: "Updated At",
                        align: "left",
                      },
                    ]}
                    data={staffList}
                    onDeleteHandler={onDeleteStaffHandler}
                    onEditHandler={onEditStaffDetailsHandler}
                  />
                </div>
                <PaginationComponent
                  totalCount={pageData?.total_count}
                  pageSize={pageData?.page_size}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  onPageChange={fetchData}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}

import { useNavigate, useParams } from "react-router-dom";
import CommonFooter from "../../../components/layout/common_footer";
import CommonHeader from "../../../components/layout/common_header";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleCustomError } from "../../../hooks/handleCustomError";
import { useEffect, useMemo } from "react";
import WebLoader from "../../../components/webLoader";
import { useGetUserProfileQuery } from "../../../feature/api/userProfileDataApiSlice";

const ViewUser = () => {
  const navigation = useNavigate();
  const { id } = useParams();
  const userId = parseInt(id);

  /* **************** Start fetching single user data ******************* */
  let { data: singleUser, isLoading: isUserLoading } = useGetUserProfileQuery({
      user_id: userId,
    });

  const userData = useMemo(() => {
    return singleUser?.data || null;
  }, [singleUser]);
  /* **************** Start fetching single user data ******************* */

  useEffect(() => {
    if (isUserLoading) return;

    if (!userData) {
      handleCustomError("Error was found, please try again later!");
      navigation("/dashboard");
    }
  }, [isUserLoading, userData, navigation]);
  

  const activePage = "User Profile";
  const linkHref = "/dashboard";

  /* **************** Start Web Loader  ******************* */
            if (isUserLoading)
              return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">User Details</h4>
                                <div className="row">
                                {userData?.role_id != 1 &&
                                <>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_id"
                                        className="form-label"
                                      >
                                        Branch{" "}
                                      </label>
                                      <p>{userData?.branch_name}</p>
                                    </div>
                                  </div>
                                  <div className="col-lg-6"></div>
                                  </>
                                  }
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="staff_role_id"
                                        className="form-label"
                                      >
                                        Role{" "}
                                      </label>
                                      <p>{userData?.role_name}</p>
                                    </div>
                                  </div>
                                  {userData?.role_id !=1 &&                                  
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="staff_role_id"
                                        className="form-label"
                                      >
                                        Staff Role{" "}
                                      </label>
                                      <p>{userData?.staff_role_name}</p>
                                    </div>
                                  </div>
                                    }
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="name"
                                        className="form-label"
                                      >
                                        Name{" "}
                                      </label>
                                      <p>{userData?.name}</p>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="email"
                                        className="form-label"
                                      >
                                        Email Address{" "}
                                      </label>
                                      <p>{userData?.email}</p>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="email"
                                        className="form-label"
                                      >
                                        Phone{" "}
                                      </label>
                                      <p>{userData?.phone_code}{userData?.phone}</p>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="qid"
                                        className="form-label"
                                      >
                                        Qatar ID{" "}
                                      </label>
                                      <p>{userData?.qid}</p>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="address"
                                        className="form-label"
                                      >
                                        Address{" "}
                                      </label>
                                      <p>{userData?.address}</p>
                                    </div>
                                  </div>
                                </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
};

export default ViewUser;

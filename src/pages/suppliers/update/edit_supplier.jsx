import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import { useEditSupplierMutation, useSingleSupplierQuery } from "../../../feature/api/supplierDataApiSlice";
import { useNavigate, useParams } from "react-router-dom";
import WebLoader from "../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useSelector } from "react-redux";
import { useEffect, useMemo } from "react";
import { handleCustomError } from "../../../hooks/handleCustomError";

const validation = yup.object().shape({
  supplier_name: yup.string().required().label("Supplier Name"),
  phone_code: yup.string().required().label("Phone Code"),
  phone: yup.string().required().label("Phone"),
  email: yup.string().email().required().label("Email"),
  country: yup.string().required().label("Country"),
  currency_code: yup.string().required().label("Currency Code"),
  billing_address: yup.string().label("Billing Address"),
  tax_id: yup.string().label("Tax ID"),
  payment_terms: yup.string().label("Payment Terms"),
  credit_limit: yup.string().label("Credit Limit"),
  status: yup.string().required().label("Status"),
  remarks: yup.string().label("Remarks"),
  branch_id: yup.string().required().label("Branch"),
});
export default function EditSupplier() {
  const navigation = useNavigate();
  const { id } = useParams();
  const supplierId = parseInt(id);
  const activePage = "Suppliers Master";
  const linkHref = "/dashboard";

  /* **************** Start fetching single supplier data ******************* */
  let { data: singleSupplier, isLoading: isSupplierLoading } = useSingleSupplierQuery({
      supplier_id: supplierId,
  });

  const supplierData = useMemo(() => {
    return singleSupplier?.data || null;
  }, [singleSupplier]);
  /* **************** Start fetching single supplier data ******************* */

  useEffect(() => {
    if (isSupplierLoading) return;

    if (!supplierData) {
      handleCustomError("Error was found, please try again later!");
        navigation("/suppliers");
      }
    }, [isSupplierLoading, supplierData, navigation]);


  const initialValues = {
    supplier_name: supplierData?.supplier_name || null,
    phone_code: supplierData?.phone_code || "",
    phone: supplierData?.phone || "",
    country: supplierData?.country_iso3 || "",
    currency_code: supplierData?.currency_code || "",
    email: supplierData?.email || "",
    billing_address: supplierData?.billing_address || "",
    tax_id: supplierData?.tax_id || "",
    payment_terms: supplierData?.payment_terms || "",
    credit_limit: supplierData?.credit_limit || "",
    status: supplierData?.status || "",
    remarks: supplierData?.remarks || "",
    branch_id: supplierData?.branch_id || "",
    supplier_code: supplierData?.supplier_code || "",
  };

  /* **************** Start list all countries ******************* */
  const countries = useSelector((state) => state.commonState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + " (" + values.phone_code + ")",
      }))
    : [];

    //  filtering out unique currency code

    const uniqueCurrencies = [
      ...new Set(
        countries.data.map((values) => values.currency_code)
      )
    ].map((currency_code) => {
      const values = countries.data.find((v) => v.currency_code === currency_code);
      return {
        value: values.currency_code,
        label: `${values.currency_name} (${values.currency_code})`,
      };
    });
  /* **************** End list all countries ******************* */

  /* **************** Start list User Status ******************* */
  const userStatusData = useSelector((state) => state.commonState.userStatus);
  const userStatusDataList = userStatusData?.data || [];
  const userStatusList = userStatusDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list User Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Edit Supplier ******************* */
  const [handleEditSupplierApi, { isLoading: isLoading }] =
    useEditSupplierMutation();
  const handleSubmit = async (body) => {
    try {
      const createBody = {
        ...body,
        supplier_id: parseInt(supplierId),
        branch_id: parseInt(body.branch_id),
        status: parseInt(body.status),
      };
      const resp = await handleEditSupplierApi(createBody).unwrap();
      handleApiSuccess(resp);
      navigation("/suppliers");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Supplier ******************* */

  /* **************** Web Loader  ******************* */
  if (isLoading || isSupplierLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">
                              Update Supplier Details
                            </h4>
                            <p className="card-subtitle mb-4">
                              To update Supplier, add details and save from here
                            </p>
                            <Formik
                              initialValues={initialValues}
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              <Form
                                name="product-create"
                                className="needs-validation"
                                autoComplete="off"
                                encType="multipart/form-data"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_id"
                                        className="form-label"
                                      >
                                        Branch
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        name="branch_id"
                                        id="branch_id"
                                        className="form-select"
                                        type="select"
                                        options={branchesList}
                                      />
                                    </div>
                                  </div>

                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="supplier_code"
                                        className="form-label"
                                      >
                                        Supplier Code
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="supplier_code"
                                        id="supplier_code"
                                        placeholder="Supplier Code *"
                                        autoComplete="off"
                                        disabled
                                        className="form-control"
                                      />
                                    </div>
                                  </div>

                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="supplier_name"
                                        className="form-label"
                                      >
                                        Supplier Name
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="supplier_name"
                                        id="supplier_name"
                                        placeholder="Supplier Name *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="country"
                                        className="form-label"
                                      >
                                        Country
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        name="country"
                                        id="country"
                                        className="form-select"
                                        type="select"
                                        options={countries.data.map(
                                          (values) => ({
                                            value: values.iso3,
                                            label:
                                              values.name +
                                              " (" +
                                              values.iso3 +
                                              ")",
                                          })
                                        )}
                                      />
                                    </div>
                                  </div>                                  
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        className="form-label"
                                        htmlFor="phone"
                                      >
                                        Phone Number{" "}
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <FormikField
                                            name="phone_code"
                                            id="phone_code"
                                            className="form-select"
                                            type="select"
                                            options={countriesList}
                                          />
                                        </div>
                                        <div className="col-8">
                                          <FormikField
                                            type="number"
                                            name="phone"
                                            id="phone"
                                            placeholder="Phone Number *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="email"
                                        className="form-label"
                                      >
                                        Email
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="email"
                                        id="email"
                                        placeholder="Email *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="billing_address"
                                        className="form-label"
                                      >
                                        Billing Address
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="billing_address"
                                        id="billing_address"
                                        placeholder="Billing Address"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6"></div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="payment_terms"
                                        className="form-label"
                                      >
                                        Payment Terms
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="payment_terms"
                                        id="payment_terms"
                                        placeholder="Payment Terms"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="credit_limit"
                                        className="form-label"
                                      >
                                        Credit Limit
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="credit_limit"
                                        id="credit_limit"
                                        placeholder="Credit Limit"
                                        autoComplete="off"
                                        className="form-control"
                                        step="0.01"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="currency"
                                        className="form-label"
                                      >
                                        Currency
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        name="currency_code"
                                        id="currency_code"
                                        className="form-select"
                                        type="select"
                                        options={uniqueCurrencies}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="tax_id"
                                        className="form-label"
                                      >
                                        Tax ID
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="tax_id"
                                        id="tax_id"
                                        placeholder="Tax ID"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="status"
                                        className="form-label"
                                      >
                                        Status
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        name="status"
                                        id="status"
                                        className="form-select"
                                        type="select"
                                        options={userStatusList}
                                      />
                                    </div>
                                  </div>

                                  <div className="col-lg-12">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="remarks"
                                        className="form-label"
                                      >
                                        Remarks
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="remarks"
                                        id="remarks"
                                        placeholder="Remarks"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Edit Supplier
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}

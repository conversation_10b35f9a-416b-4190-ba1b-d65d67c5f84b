import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import {
  useDeleteSupplierMutation,
  useGetSupplierListQuery,
} from "../../feature/api/supplierDataApiSlice";
import { Table } from "../../components/datatable";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { useMemo, useState } from "react";
import WebLoader from "../../components/webLoader";
import { PaginationComponent } from "../../components/pagination";
import useConfirmDelete from "../../hooks/useConfirmDelete";
import { useGetShopBranchsQuery } from "../../feature/api/branchDataApiSlice";
import { useGetUserStatusQuery } from "../../feature/api/statusApiSlice";
import { useSelector } from "react-redux";

export default function SupplierList() {
  const activePage = "Supplier Master";
  const linkHref = "/dashboard";
  const navigation = useNavigate();
  /* **************** Start list all Suppliers ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterBranchId, setFilterBranchId] = useState(null);
  const [filterCountry, setFilterCountry] = useState(null);
  const [filterStatus, setFilterStatus] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: supplierListResp, isLoading } = useGetSupplierListQuery({
    page: currentPage,
    status: parseInt(filterStatus),
    branch_id: parseInt(filterBranchId),
    country: filterCountry,
    keywords: filterKeywords,
  });
  const supplierList = useMemo(() => {
    if (!supplierListResp?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return supplierListResp?.data?.list;
  }, [currentPage, supplierListResp?.data.list]);
  const pageData = useMemo(
    () => supplierListResp?.data?.page ?? null,
    [supplierListResp]
  );

  /* **************** End list all Suppliers ******************* */

  /* ****************  Start Filter ****************** */
  const handleStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleBranchFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterBranchId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleCountryFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterCountry(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start list all countries ******************* */
  const countries = useSelector((state) => state.commonState.countries);
  /* **************** End list all countries ******************* */

  /* **************** Start list User Status ******************* */
  const userStatusData = useGetUserStatusQuery();
  const userStatusDataList = userStatusData?.data?.data || [];
  const userStatusList = userStatusDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list User Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** End Filter ***************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  const { showSimpleConfirm } = useConfirmDelete({
    title: "Delete Supplier?",
    text: "Are you sure you want to delete this supplier?",
    confirmButtonText: "Yes, delete supplier!",
    cancelButtonText: "Cancel",
  });
  const [handledeleteSupplierApi, { isLoading: isDeleteLoading }] =
    useDeleteSupplierMutation();
  const onDeleteSupplierHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
    if (confirmed) {
      try {
        const body = { supplier_id: id };
        const resp = await handledeleteSupplierApi(body).unwrap();
        handleApiSuccess(resp);
        navigation("/suppliers"); // Redirect to the desired page
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };

  const onEditSupplierDetailsHandler = (d) => {
    navigation(`/editSupplier/${d.id}`);
  };

  const onViewSupplierHandler = (d) => {
    navigation(`/viewSupplier/${d.id}`);
  };

  /* **************** Web Loader  ******************* */
  if (isLoading || isDeleteLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Supplier List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/createSupplier"}
                          className="btn btn-primary"
                        >
                          Create New Supplier
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex gap-6">
                      <div>
                        <select
                          value={filterBranchId}
                          className="form-control search-chat py-2"
                          onChange={handleBranchFilter}
                        >
                          <option value="">All Branches</option>
                          {branchesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterCountry}
                          className="form-control search-chat py-2"
                          onChange={handleCountryFilter}
                        >
                          <option value="">All Countries</option>
                          {countries?.data.map((option) => (
                            <option key={option.iso3} value={option.iso3}>
                              {option.name + " (" + option.iso3 + ")"}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2"
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {userStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "supplier_code",
                        label: "Supplier Code",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/viewSupplier/${row.id}`,
                        }),
                      },
                      {
                        key: "supplier_name",
                        label: "Name",
                        align: "left",
                      },
                      {
                        key: "email",
                        label: "Email",
                        align: "left",
                      },
                      {
                        key: "full_phone",
                        label: "Phone",
                        align: "left",
                      },
                      {
                        key: "branch_name",
                        label: "Branch",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/editBranch/${row.branch_id}`,
                        }),
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                    ]}
                    data={supplierList}
                    onDeleteHandler={onDeleteSupplierHandler}
                    onEditHandler={onEditSupplierDetailsHandler}
                    customActions={[
                      {
                        label: "View",
                        icon: "ti ti-eye",
                        handler: onViewSupplierHandler,
                      },
                    ]}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}

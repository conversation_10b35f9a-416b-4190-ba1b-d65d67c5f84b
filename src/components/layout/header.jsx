import { useEffect } from "react";
import SideBarMenu from "./sidebarmenu";
import UseThemeSettings from "../../hooks/useThemeSettings";
import { useDispatch, useSelector } from "react-redux";
import { setMenuModule } from "../../feature/slice/appConfigSlice";
export default function Header() {
  const { toggleSidebarType } = UseThemeSettings();

  const dispatch = useDispatch(); // Initialize the dispatch hook
  const menuModule = useSelector((state) => state.appConfig.menuModule);

  useEffect(() => {
    if (!menuModule) {
      dispatch(setMenuModule("mini-1"));
    }
    document.querySelectorAll(".mini-nav-item").forEach((item) => {
      item.classList.remove("selected");
    });
    document.getElementById(menuModule).classList.add("selected");
    document.querySelectorAll(".sidebar-nav").forEach((item) => {
      item.classList.remove("d-block");
    });
    document
      .getElementById("menu-right-" + menuModule)
      ?.classList.add("d-block");
  }, [menuModule, dispatch]);

  useEffect(() => {
    if (window.bootstrap) {
      const tooltipElements = document.querySelectorAll(
        '[data-bs-toggle="tooltip"]'
      );
      tooltipElements.forEach((tooltip) => {
        new window.bootstrap.Tooltip(tooltip);
      });
    } else {
      console.error("Bootstrap JavaScript is not loaded");
    }
  }, []);

  const handleMiniNavClick = (id) => {
    dispatch(setMenuModule(id));
  };
  return (
    <>
      <aside className="side-mini-panel with-vertical">
        <div>
          <div className="iconbar">
            <div>
              <div className="mini-nav">
                <div className="brand-logo d-flex align-items-center justify-content-center">
                  <a
                    className="nav-link sidebartoggler"
                    id="headerCollapse"
                    onClick={toggleSidebarType}
                  >
                    <iconify-icon
                      icon="solar:hamburger-menu-line-duotone"
                      class="fs-7"
                    ></iconify-icon>
                  </a>
                </div>
                <ul className="mini-nav-ul" data-simplebar>
                  <li
                    className="mini-nav-item "
                    id="mini-1"
                    onClick={() => handleMiniNavClick("mini-1")}
                  >
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Dashboards "
                    >
                      <iconify-icon
                        icon="solar:layers-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  <li
                    className="mini-nav-item"
                    id="mini-2"
                    onClick={() => handleMiniNavClick("mini-2")}
                  >
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Product Master"
                    >
                      <iconify-icon
                        icon="solar:box-linear"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  <li
                    className="mini-nav-item"
                    id="mini-3"
                    onClick={() => handleMiniNavClick("mini-3")}
                  >
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Invoice Master"
                    >
                      <iconify-icon
                        icon="solar:bill-linear"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  <li
                    className="mini-nav-item"
                    id="mini-4"
                    onClick={() => handleMiniNavClick("mini-4")}
                  >
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Purchase Master"
                    >
                      <iconify-icon
                        icon="solar:bag-linear"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  <li
                    className="mini-nav-item"
                    id="mini-5"
                    onClick={() => handleMiniNavClick("mini-5")}
                  >
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Customer Master"
                    >
                      <iconify-icon
                        icon="solar:user-circle-outline"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  {/* <li className="mini-nav-item" id="mini-4">
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Forms"
                    >
                      <iconify-icon
                        icon="solar:palette-round-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>

                  <li>
                    <span className="sidebar-divider lg"></span>
                  </li>
                  <li className="mini-nav-item" id="mini-5">
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Tables"
                    >
                      <iconify-icon
                        icon="solar:tuning-square-2-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  <li className="mini-nav-item" id="mini-6">
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Charts"
                    >
                      <iconify-icon
                        icon="solar:chart-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>

                  <li className="mini-nav-item" id="mini-8">
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Components"
                    >
                      <iconify-icon
                        icon="solar:archive-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>

                  <li>
                    <span className="sidebar-divider lg"></span>
                  </li>
                  <li className="mini-nav-item" id="mini-9">
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Authentication Pages"
                    >
                      <iconify-icon
                        icon="solar:lock-keyhole-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li>
                  <li className="mini-nav-item" id="mini-10">
                    <a
                      data-bs-toggle="tooltip"
                      data-bs-custom-class="custom-tooltip"
                      data-bs-placement="right"
                      data-bs-title="Docs &amp; Other"
                    >
                      <iconify-icon
                        icon="solar:mirror-left-line-duotone"
                        class="fs-7"
                      ></iconify-icon>
                    </a>
                  </li> */}
                </ul>
              </div>
              <SideBarMenu />
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}

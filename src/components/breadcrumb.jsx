import { Link } from "react-router-dom";

const Breadcrumb = ({ activePage, linkHref }) => {
  return (
    <>
      <div className="card card-body py-3">
        <div className="row align-items-center">
          <div className="col-12">
            <div className="d-sm-flex align-items-center justify-space-between">
              <h4 className="mb-4 mb-sm-0 card-title">{activePage}</h4>
              <nav aria-label="breadcrumb" className="ms-auto">
                <ol className="breadcrumb">
                  <li className="breadcrumb-item d-flex align-items-center">
                    <Link
                      className="text-muted text-decoration-none d-flex"
                      to={linkHref}
                    >
                      <iconify-icon
                        icon="solar:home-2-line-duotone"
                        className="fs-6"
                      ></iconify-icon>
                    </Link>
                  </li>
                  <li className="breadcrumb-item" aria-current="page">
                    <span className="badge fw-medium fs-2 bg-primary-subtle text-primary">
                      {activePage}
                    </span>
                  </li>
                </ol>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default Breadcrumb;

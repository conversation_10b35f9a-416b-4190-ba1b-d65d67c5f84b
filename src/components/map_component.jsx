import { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";

const MapComponent = ({ location, setLocation }) => {
  const mapRef = useRef(null);
  const markerRef = useRef(null);
  const [map, setMap] = useState(null);
  const [geocoder, setGeocoder] = useState(null);
  const [infowindow, setInfowindow] = useState(null);
  // const [location, setLocation] = useState({
  //   lat: latitude,
  //   lng: longitude,
  // });
  const [address, setAddress] = useState("");

  const initializeMap = () => {
    const google = window.google;

    const mapInstance = new google.maps.Map(mapRef.current, {
      center: location,
      zoom: 18,
    });

    const markerInstance = new google.maps.Marker({
      position: location,
      map: mapInstance,
      draggable: true,
    });

    const geocoderInstance = new google.maps.Geocoder();
    const infowindowInstance = new google.maps.InfoWindow();

    setMap(mapInstance);

    markerRef.current = markerInstance;
    setGeocoder(geocoderInstance);
    setInfowindow(infowindowInstance);

    // Event Listener for Marker Drag End
    markerInstance.addListener("dragend", () => {
      const newPosition = markerInstance.getPosition();
      setLocation({
        lat: newPosition.lat(),
        lng: newPosition.lng(),
      });
      geocodePosition(newPosition);
      setAddress(newPosition.formatted_address);
    });
  };

  const geocodePosition = (position) => {
    geocoder.geocode({ location: position }, (results, status) => {
      if (status === "OK" && results[0]) {
        infowindow.setContent(results[0].formatted_address);
        infowindow.open(map, markerRef.current);
        setAddress(results[0].formatted_address);
      } else {
        setAddress("Address not found");
      }
    });
  };

  const handleSearch = (e) => {
    const google = window.google;
    const searchBox = new google.maps.places.SearchBox(e.target);
    map.addListener("bounds_changed", () => {
      searchBox.setBounds(map.getBounds());
    });
    searchBox.addListener("places_changed", () => {
      const places = searchBox.getPlaces();
      if (places && places.length > 0) {
        const place = places[0];
        const location = place.geometry.location;
        setLocation({
          lat: location.lat(),
          lng: location.lng(),
        });
        map.setCenter(location);
        markerRef.current.setPosition(location);
        setAddress(place.formatted_address);
      }
    });
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const currentLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setLocation(currentLocation);
          map.setCenter(currentLocation);
          markerRef.current.setPosition(currentLocation);
          geocodePosition(currentLocation);
        },
        () => {
          alert("Unable to fetch your location. Please try again.");
        }
      );
    } else {
      alert("Geolocation is not supported by your browser.");
    }
  };

  useEffect(() => {
    //
    if (markerRef.current && map) {
      markerRef.current.setPosition(location);
      map.setCenter(location);
    } else {
      initializeMap();
    }
  }, [location]);

  return (
    <div>
      <div className="row">
        <div className="col-8">
        <input
          id="searchBox"
          className="search_box form-control"
          type="text"
          placeholder="Search Location ....."
          onChange={handleSearch}
        />
        </div>
        <div className="col-4">
        <button
          onClick={getCurrentLocation}
          id="chooseCurrentLocation"
          className="chooseCurrentLocation btn btn-info"
          type="button"
        >
          <iconify-icon icon="solar:map-point-linear" style={{'margin-right': '3px'}}></iconify-icon>
          Change to Current Location 
        </button>
        </div>
      </div>
      <br></br>
      <div
        id="map"
        ref={mapRef}
        style={{ height: "400px", width: "100%" }}
      ></div>
      <br />
      <p className="current_place" id="current_place">
        {address}
      </p>
      <input
        style={{ display: "none" }}
        id="latitude"
        className="mapLatCustom"
        name="latitude"
        type="text"
        value={location.lat}
        readOnly
      />
      <input
        style={{ display: "none" }}
        id="longitude"
        className="mapLngCustom"
        name="longitude"
        type="text"
        value={location.lng}
        readOnly
      />
    </div>
  );
};

// Add PropTypes validation
MapComponent.propTypes = {
  location: {
    lat: PropTypes.number.isRequired,
    lnt: PropTypes.number.isRequired,
  }, // Ensures latitude is a number and required
  setLocation: PropTypes.any, // Ensures longitude is a number and required
};
export default MapComponent;

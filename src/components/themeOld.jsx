import { useState, useEffect } from "react";

const OldThemeSettings = () => {
  const [settings, setSettings] = useState({
    Layout: "vertical", // default value
    SidebarType: "full",
    BoxedLayout: false,
    Direction: "ltr",
    Theme: "light",
    ColorTheme: "default",
    cardBorder: false,
  });

  useEffect(() => {
    AdminSettingsInit();
  }, [settings]);

  const AdminSettingsInit = () => {
    ManageThemeLayout(); // done
    ManageSidebarType(); // Done
    ManageBoxedLayout(); // done
    ManageDirectionLayout(); // done
    ManageDarkThemeLayout(); // done
    ManageColorThemeLayout();
    ManageCardLayout(); // done
  };

  const ManageThemeLayout = () => {
    switch (settings.Layout) {
      case "horizontal":
        document.documentElement.setAttribute("data-layout", "horizontal");
        break;
      case "vertical":
        document.documentElement.setAttribute("data-layout", "vertical");
        break;
      default:
        break;
    }
  };

  const ManageSidebarType = () => {
    switch (settings.SidebarType) {
      case "full":
        document.body.setAttribute("data-sidebartype", "full");
        break;
      case "mini-sidebar":
        document.body.setAttribute("data-sidebartype", "mini-sidebar");
        break;
      default:
        break;
    }
  };

  const ManageBoxedLayout = () => {
    if (settings.BoxedLayout) {
      document.documentElement.setAttribute("data-boxed-layout", "boxed");
    } else {
      document.documentElement.setAttribute("data-boxed-layout", "full");
    }
  };

  const ManageDirectionLayout = () => {
    switch (settings.Direction) {
      case "ltr":
        document.documentElement.setAttribute("dir", "ltr");
        break;
      case "rtl":
        document.documentElement.setAttribute("dir", "rtl");
        break;
      default:
        break;
    }
  };

  const ManageDarkThemeLayout = () => {
    if (settings.Theme === "light") {
      document.documentElement.setAttribute("data-bs-theme", "light");
    } else if (settings.Theme === "dark") {
      document.documentElement.setAttribute("data-bs-theme", "dark");
    }
  };

  const ManageColorThemeLayout = () => {
    document.documentElement.setAttribute(
      "data-color-theme",
      settings.ColorTheme
    );
  };

  const ManageCardLayout = () => {
    if (settings.cardBorder) {
      document.documentElement.setAttribute("data-card", "border");
    } else {
      document.documentElement.setAttribute("data-card", "shadow");
    }
  };

  const handleDirection = (dir) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      Direction: dir,
    }));
  };

  const handleTheme = (theme) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      Theme: theme,
    }));
  };

  const handleLayout = (layout) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      Layout: layout,
    }));
  };

  const handleSidebarType = (sidebarType) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      SidebarType: sidebarType,
    }));
  };

  const handleCardLayout = (cardType) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      cardBorder: cardType === "border",
    }));
  };

  const handleBoxedLayout = (boxedLayout) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      BoxedLayout: boxedLayout,
    }));
  };

  return (
    <div>
      <div>
        <h3>Layout Type</h3>
        <button onClick={() => handleLayout("horizontal")}>Horizontal</button>
        <button onClick={() => handleLayout("vertical")}>Vertical</button>
      </div>

      <div>
        <h3>Direction</h3>
        <button onClick={() => handleDirection("ltr")}>LTR</button>
        <button onClick={() => handleDirection("rtl")}>RTL</button>
      </div>

      <div>
        <h3>Theme Mode</h3>
        <button onClick={() => handleTheme("light")}>Light</button>
        <button onClick={() => handleTheme("dark")}>Dark</button>
      </div>

      <div>
        <h3>Sidebar Type</h3>
        <button onClick={() => handleSidebarType("full")}>Full Sidebar</button>
        <button onClick={() => handleSidebarType("mini-sidebar")}>
          Mini Sidebar
        </button>
      </div>

      <div>
        <h3>Card Type</h3>
        <button onClick={() => handleCardLayout("border")}>Border</button>
        <button onClick={() => handleCardLayout("shadow")}>Shadow</button>
      </div>

      <div>
        <h3>Layout Boxed or Full</h3>
        <button onClick={() => handleBoxedLayout(true)}>Boxed</button>
        <button onClick={() => handleBoxedLayout(false)}>Full</button>
      </div>
    </div>
  );
};

export default OldThemeSettings;

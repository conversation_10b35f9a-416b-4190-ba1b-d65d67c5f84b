import { useMemo } from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";

export const Table = (props) => {
  const { headCells, data, onDeleteHandler, onEditHandler, customActions = [] } = props;

  const hasActions = useMemo(
    () => Boolean(onDeleteHandler || onEditHandler || customActions.length > 0),
    [onEditHandler, onDeleteHandler, customActions]
  );

  const tableHeader = useMemo(
    () =>
      headCells.map((h) => (
        <th key={h.key} width={h.width} align={h.align ?? "left"}>
          <h6 className="fs-4 fw-semibold mb-0">{h.label}</h6>
        </th>
      )),
    [headCells]
  );

  const renderRows = useMemo(
    () =>
      data?.map((d) => (
        <tr key={d.id}>
          {headCells.map((h) => (
            <td key={h.id} width={h.width} align={h.align}>
              {h.linkTo && (
                <Link
                to={typeof h.linkTo === "function" ? h.linkTo(d).to : h.linkTo}
                state={typeof h.linkTo === "function" ? h.linkTo(d).state : undefined}
                rel="noreferrer"
                target={ h.linkTo(d).newTab === true ? "_blank" : "_self"}
                style={{color: 'var(--bs-primary)', textDecoration: 'underline', textDecorationStyle: 'solid',textDecorationColor: 'var(--bs-primary)'}}
                >
                {d[h.key]}
              </Link>
              )}
              {h.key_id === "status" ? (
                d[h.key_id] == 1 ? (
                  <span
                    type="button"
                    className="badge bg-warning-subtle text-warning"
                  >
                    {d[h.key]}
                  </span>
                ) : d[h.key_id] == 2 ? (
                  <span
                    type="button"
                    className="badge bg-success-subtle text-success"
                  >
                    {d[h.key]}
                  </span>
                ) : d[h.key_id] == 3 ? (
                  <span
                    type="button"
                    className="badge bg-primary-subtle text-primary"
                  >
                    {d[h.key]}
                  </span>
                ) : d[h.key_id] == 4 ? (
                  <span
                    type="button"
                    className="badge bg-danger-subtle text-danger"
                  >
                    {d[h.key]}
                  </span>
                ) : null
              ) : h.key_id === "stock_status" ? (
                d[h.key_id] == 1 ? (
                  <span
                    type="button"
                    className="badge bg-success-subtle text-success"
                  >
                    {d[h.key]}
                  </span>
                ) : d[h.key_id] == 2 ? (
                  <span
                    type="button"
                    className="badge bg-danger-subtle text-danger"
                  >
                    {d[h.key]}
                  </span>
                ) : null
              ): h.key_id === "action" ? (
                d[h.key_id] == 1 ? (
                  <span
                    type="button"
                    className="badge bg-success-subtle text-success"
                  >
                    {d[h.key]}
                  </span>
                ) : d[h.key_id] == 2 ? (
                  <span
                    type="button"
                    className="badge bg-warning-subtle text-warning"
                  >
                    {d[h.key]}
                  </span>
                ) : d[h.key_id] == 3 ? (
                  <span
                    type="button"
                    className="badge bg-danger-subtle text-danger"
                  >
                    {d[h.key]}
                  </span>
                ) : null
              ) : !h.linkTo && !h.key_id && (
                <p className="mb-0 fw-semibold">{d[h.key]}</p>
              )}
            </td>
          ))}
          {hasActions && (
            <td>
              {/* {renderEditButton}
              {renderDeleteButton} */}
              <div className="dropdown dropstart">
                <a
                  className="text-muted"
                  id="dropdownMenuButton"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <i className="ti ti-dots-vertical fs-6"></i>
                </a>
                <ul
                  className="dropdown-menu"
                  aria-labelledby="dropdownMenuButton"
                >
                  {onEditHandler && (
                    <li>
                      <button
                        className="dropdown-item d-flex align-items-center gap-3"
                        onClick={() => onEditHandler(d)}
                      >
                        <i className="fs-4 ti ti-edit"></i>Edit
                      </button>
                    </li>
                  )}
                {customActions.map((action, index) => (
                  <li key={index}>
                    <button
                    className="dropdown-item d-flex align-items-center gap-3"
                      onClick={() => action.handler(d)}>
                        <i className={`fs-4 ${action.icon}`}></i>{action.label}</button></li>))}
                  {onDeleteHandler && (
                    <li>
                      <button
                        className="dropdown-item d-flex align-items-center gap-3"
                        onClick={() => onDeleteHandler(d.id)}
                      >
                        <i className="fs-4 ti ti-trash"></i>Delete
                      </button>
                    </li>
                  )}
                </ul>
              </div>
            </td>
          )}
        </tr>
      )),
    [data, hasActions, headCells, onDeleteHandler, onEditHandler, customActions]
  );

  return (
    <table className="table table-striped table-bordered display text-nowrap">
      <thead className="text-dark fs-4">
        <tr>
          {tableHeader}
          {hasActions && <th>Action</th>}
        </tr>
      </thead>
      {renderRows.length === 0 ? (
        <tbody>
          <tr>
            <td
              colSpan={tableHeader.length + 1}
              style={{ "text-align": "center", "font-weight": "600" }}
            >
              No data found!
            </td>
          </tr>
        </tbody>
      ) : (
        <tbody>{renderRows}</tbody>
      )}
    </table>
  );
};

// Define prop types for the Table component
Table.propTypes = {
  headCells: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      width: PropTypes.string,
      align: PropTypes.oneOf(["left", "right", "center"]),
      linkTo: PropTypes.func,
      newTab: PropTypes.bool,
    })
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.any).isRequired,
  onDeleteHandler: PropTypes.func,
  onEditHandler: PropTypes.func,
  customActions: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      icon: PropTypes.string.isRequired,
      handler: PropTypes.func.isRequired,
    })
  ),
};

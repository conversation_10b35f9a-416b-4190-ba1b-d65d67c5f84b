import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  countries: [],
};

const commonSlice = createSlice({
  name: "commonState",
  initialState,
  reducers: {
    setCountries(state, action) {
      state.countries = action?.payload || [];
    },
    clearCountries(state) {
      state.countries = [];
    },
    setGeneralStatuses(state, action) {
      state.generalStatus = action?.payload || [];
    },
    cleartGeneralStatuses(state) {
      state.generalStatus = [];
    },
    setUserStatuses(state, action) {
      state.userStatus = action?.payload || [];
    },
    cleartUserStatuses(state) {
      state.userStatus = [];
    },
    setStaffStatuses(state, action) {
      state.staffStatus = action?.payload || [];
    },
    cleartStaffStatuses(state) {
      state.staffStatus = [];
    },
    setShopStatuses(state, action) {
      state.shopStatus = action?.payload || [];
    },
    cleartShopStatuses(state) {
      state.shopStatus = [];
    },
    setBranchStatuses(state, action) {
      state.branchStatus = action?.payload || [];
    },
    cleartBranchStatuses(state) {
      state.branchStatus = [];
    },
    setBranchTypes(state, action) {
      state.branchTypes = action?.payload || [];
    },
    cleartBranchTypes(state) {
      state.branchTypes = [];
    },
    setInvoiceTypes(state, action) {
      state.invoiceTypes = action?.payload || [];
    },
    cleartInvoiceTypes(state) {
      state.invoiceTypes = [];
    },
    setInvoiceStatuses(state, action) {
      state.invoiceStatus = action?.payload || [];
    },
    cleartInvoiceStatuses(state) {
      state.invoiceStatus = [];
    },
  },
});

export const {
  setCountries,
  clearCountries,
  setGeneralStatuses,
  cleartGeneralStatuses,
  setBranchStatuses,
  cleartBranchStatuses,
  setUserStatuses,
  cleartUserStatuses,
  setShopStatuses,
  cleartShopStatuses,
  setStaffStatuses,
  cleartStaffStatuses,
  setBranchTypes,
  cleartBranchTypes,
  setInvoiceTypes,
  cleartInvoiceTypes,
  setInvoiceStatuses,
  cleartInvoiceStatuses
} = commonSlice.actions;
export default commonSlice.reducer;

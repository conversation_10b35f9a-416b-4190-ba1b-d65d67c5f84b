import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  themeLayout: "vertical",
  sidebarValue: "full",
  themeDarkLight: "light",
  themeDirection: "ltr",
  themeBoxLayout: "full",
  cardLayout: "shadow",
  colorTheme: "Blue_Theme",
  menuModule: "mini-1",
};

const themeSlice = createSlice({
  name: "appConfig",
  initialState,
  reducers: {
    setThemeLayout(state, action) {
      state.themeLayout = action?.payload || null;
    },
    setSidebarValue(state, action) {
      state.sidebarValue = action?.payload || null;
    },
    setThemeDarkLight(state, action) {
      state.themeDarkLight = action?.payload || null;
    },
    setThemeDirection(state, action) {
      state.themeDirection = action?.payload || null;
    },
    setThemeBoxLayout(state, action) {
      state.themeBoxLayout = action?.payload || null;
    },
    setCardLayout(state, action) {
      state.cardLayout = action?.payload || null;
    },
    setColorTheme(state, action) {
      state.colorTheme = action?.payload || null;
    },
    setMenuModule(state, action) {
      state.menuModule = action?.payload || null;
    },
  },
});

export const {
  setThemeLayout,
  setSidebarValue,
  setThemeDarkLight,
  setThemeDirection,
  setThemeBoxLayout,
  setCardLayout,
  setColorTheme,
  setMenuModule,
} = themeSlice.actions;
export default themeSlice.reducer;

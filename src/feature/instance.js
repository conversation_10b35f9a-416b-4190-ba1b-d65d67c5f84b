import { fetchBaseQuery } from "@reduxjs/toolkit/query";
import baseUrl from "./base_url";
import { setToken, setUserId, setUserType } from "./slice/authSlice";

export const baseQueryWithInterceptor = async (args, api, extraOptions) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: baseUrl,
    credentials: "include", // Enable if your login endpoint requires cookies or auth headers
    prepareHeaders: (headers, { getState }) => {
      const state = getState();
      const token = state?.authState?.token;
      const username = "admin"; // Replace with secure handling
      const password = "password"; // Replace with secure handling
      const basicAuth = btoa(`${username}:${password}`);
      headers.set("Authorization", `Basic ${basicAuth}`);    
      if (token) {
        headers.set("token", `${token}`);
      }
      return headers;
    },
  });

  const result = await baseQuery(args, api, extraOptions);
  if (result.error && result.error.status === 401) {
    api.dispatch(setToken(null));
    api.dispatch(setUserId(null));
    api.dispatch(setUserType(""));
    return {
      error: {
        status: 401,
        message: "Unauthorized. Please log in again.",
      },
    };
  }

  return result;
};

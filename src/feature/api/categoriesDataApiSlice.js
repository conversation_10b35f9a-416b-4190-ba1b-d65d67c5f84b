import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const categoriesDataApiSlice = createApi({
  reducerPath: "categoriesDataApiSlice",
  baseQuery,
  tagTypes: ["categoriesData"],
  endpoints: (builder) => ({
    getCategories: builder.query({
      query: (body) => ({
        url: `categories/listCategories`,
        method: "POST",
        body,
      }),
      providesTags: ["categoriesData"],
    }),
    listAllCategories: builder.query({
      query: () => ({
        url: `categories/listAllCategories`,
        method: "GET",
      }),
      providesTags: ["categoriesData"],
    }),
    deleteCategories: builder.mutation({
      query: (body) => ({
        url: `categories/deleteCategory`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["categoriesData"],
    }),
    createCategories: builder.mutation({
      query: (body) => ({
        url: `categories/createCategory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["categoriesData"],
    }),
    editCategories: builder.mutation({
      query: (body) => ({
        url: `categories/EditCategory`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["categoriesData"],
    }),
    singleCategories: builder.mutation({
      query: (body) => ({
        url: `categories/singleCategory`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetCategoriesQuery,
  useListAllCategoriesQuery,
  useDeleteCategoriesMutation,
  useCreateCategoriesMutation,
  useEditCategoriesMutation,
  useSingleCategoriesMutation,
} = categoriesDataApiSlice;

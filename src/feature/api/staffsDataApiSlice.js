import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const staffsDataApiSlice = createApi({
  reducerPath: "staffsDataApiSlice",
  baseQuery,
  tagTypes: ["staffsData", "singleStaff"],
  endpoints: (builder) => ({
    getAllStaffs: builder.query({
      query: (body) => ({
        url: `staff/listShopAllStaff`,
        method: "POST",
        body,
      }),
      providesTags: ["staffsData"],
    }),
    createStaff: builder.mutation({
      query: (body) => ({
        url: `staff/createStaff`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["staffsData"],
    }),
    deleteStaff: builder.mutation({
      query: (body) => ({
        url: `staff/deleteStaff`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["staffsData"],
    }),
    updateStaff: builder.mutation({
      query: (body) => ({
        url: `staff/updateSingleStaff`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["staffsData", "singleStaff"],
    }),
    getSingleStaffs: builder.query({
      query: (body) => ({
        url: `staff/singleStaff`,
        method: "POST",
        body,
      }),
      providesTags: ["singleStaff"],
    }),
  }),
});

export const {
  useGetAllStaffsQuery,
  useCreateStaffMutation,
  useDeleteStaffMutation,
  useUpdateStaffMutation,
  useGetSingleStaffsQuery,
} = staffsDataApiSlice;

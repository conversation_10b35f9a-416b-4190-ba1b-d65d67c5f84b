import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const customerAttachmentsDataApiSlice = createApi({
  reducerPath: "customerAttachmentsDataApiSlice",
  baseQuery,
  tagTypes: ["customerAttachmentsData"],
  endpoints: (builder) => ({
    getCustomerAttachmentsList: builder.query({
      query: (body) => ({
        url: `attachments/listAttachments`,
        method: "POST",
        body,
      }),
      providesTags: ["customerAttachmentsData"],
    }),
    // listAllCustomerAttachments: builder.query({
    //   query: (body) => ({
    //     url: `attachments/listAllCustomerAttachments`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
    deleteCustomerAttachments: builder.mutation({
      query: (body) => ({
        url: `attachments/deleteAttachments`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["customerAttachmentsData"],
    }),
    createCustomerAttachments: builder.mutation({
      query: (body) => ({
        url: `attachments/createAttachment`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["customerAttachmentsData"],
    }),
    editCustomerAttachments: builder.mutation({
      query: (body) => ({
        url: `attachments/editAttachment`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["customerAttachmentsData"],
    }),
    singleCustomer: builder.mutation({
      query: (body) => ({
        url: `attachments/singleAttachments`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetCustomerAttachmentsListQuery,
  // useListAllCustomerAttachmentsQuery,
  useDeleteCustomerAttachmentsMutation,
  useCreateCustomerAttachmentsMutation,
  useEditCustomerAttachmentsMutation,
  useSingleCustomerAttachmentsMutation,
} = customerAttachmentsDataApiSlice;

import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const rolesDataApiSlice = createApi({
  reducerPath: "rolesDataApiSlice",
  baseQuery,
  tagTypes: ["rolesData"],
  endpoints: (builder) => ({
    getRoles: builder.query({
      query: (body) => ({
        url: `staffRoles/listStaffRole`,
        method: "POST",
        body,
      }),
      providesTags: ["rolesData"],
    }),

    deleteRole: builder.mutation({
      query: (body) => ({
        url: `staffRoles/deleteStaffRole`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["rolesData"],
    }),

    createRole: builder.mutation({
      query: (body) => ({
        url: `staffRoles/createStaffRole`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["rolesData"],
    }),
    editRole: builder.mutation({
      query: (body) => ({
        url: `staffRoles/editStaffRole`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["rolesData"],
    }),
  }),
});

export const {
  useGetRolesQuery,
  useDeleteRoleMutation,
  useCreateRoleMutation,
  useEditRoleMutation,
} = rolesDataApiSlice;

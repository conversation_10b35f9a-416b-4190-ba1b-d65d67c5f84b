import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const supplierContactsDataApiSlice = createApi({
  reducerPath: "supplierContactsDataApiSlice",
  baseQuery,
  tagTypes: ["supplierContactsData"],
  endpoints: (builder) => ({
    getSupplierContactsList: builder.query({
      query: (body) => ({
        url: `supplierContacts/listSupplierContact`,
        method: "POST",
        body,
      }),
      providesTags: ["supplierContactsData"],
    }),
    // listAllSupplierContacts: builder.query({
    //   query: (body) => ({
    //     url: `supplierContacts/contacts/listAllSupplierContacts`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
    deleteSupplierContacts: builder.mutation({
      query: (body) => ({
        url: `supplierContacts/deleteSupplierContact`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["supplierContactsData"],
    }),
    createSupplierContacts: builder.mutation({
      query: (body) => ({
        url: `supplierContacts/createSupplierContact`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["supplierContactsData"],
    }),
    editSupplierContacts: builder.mutation({
      query: (body) => ({
        url: `supplierContacts/editSupplierContact`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["supplierContactsData"],
    }),
    // singleSupplier: builder.mutation({
    //   query: (body) => ({
    //     url: `suppliers/singleSupplier`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
  }),
});

export const {
  useGetSupplierContactsListQuery,
  // useListAllSupplierContactsQuery,
  useDeleteSupplierContactsMutation,
  useCreateSupplierContactsMutation,
  useEditSupplierContactsMutation,
  //   useSingleSupplierContactsMutation,
} = supplierContactsDataApiSlice;

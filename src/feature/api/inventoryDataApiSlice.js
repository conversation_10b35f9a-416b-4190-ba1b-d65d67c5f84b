import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const inventoryDataApiSlice = createApi({
  reducerPath: "inventoryDataApiSlice",
  baseQuery,
  tagTypes: ["inventoryData", "inventoryHistoryData"],
  endpoints: (builder) => ({
    getInventoryList: builder.query({
      query: (body) => ({
        url: `inventory/listInventory`,
        method: "POST",
        body,
      }),
      providesTags: ["inventoryData"],
    }),
    deleteInventory: builder.mutation({
      query: (body) => ({
        url: `inventory/deleteInventory`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["inventoryData"],
    }),
    createInventory: builder.mutation({
      query: (body) => ({
        url: `inventory/createInventory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["inventoryData"],
    }),
    editInventory: builder.mutation({
      query: (body) => ({
        url: `inventory/editInventory`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["inventoryData", "inventoryHistoryData"],
    }),
    getInventoryHistoryList: builder.query({
      query: (body) => ({
        url: `inventoryHistory/listInventoryHistory`,
        method: "POST",
        body,
      }),
      providesTags: ["inventoryHistoryData"],
    }),
  }),
});

export const {
  useGetInventoryListQuery,
  useDeleteInventoryMutation,
  useCreateInventoryMutation,
  useEditInventoryMutation,
  useGetInventoryHistoryListQuery,
} = inventoryDataApiSlice;

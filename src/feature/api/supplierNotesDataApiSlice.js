import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const supplierNotesDataApiSlice = createApi({
  reducerPath: "supplierNotesDataApiSlice",
  baseQuery,
  tagTypes: ["supplierNotesData"],
  endpoints: (builder) => ({
    getSupplierNotesList: builder.query({
      query: (body) => ({
        url: `supplierNotes/listSupplierNotes`,
        method: "POST",
        body,
      }),
      providesTags: ["supplierNotesData"],
    }),
    // listAllSupplierNotes: builder.query({
    //   query: (body) => ({
    //     url: `Notes/listAllSupplierNotes`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
    deleteSupplierNotes: builder.mutation({
      query: (body) => ({
        url: `supplierNotes/deleteSupplierNote`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["supplierNotesData"],
    }),
    createSupplierNotes: builder.mutation({
      query: (body) => ({
        url: `supplierNotes/createSupplierNotes`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["supplierNotesData"],
    }),
    editSupplierNotes: builder.mutation({
      query: (body) => ({
        url: `supplierNotes/editSupplierNotes`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["supplierNotesData"],
    }),
    // singleSupplier: builder.mutation({
    //   query: (body) => ({
    //     url: `suppliers/singleSupplier`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
  }),
});

export const {
  useGetSupplierNotesListQuery,
  // useListAllSupplierNotesQuery,
  useDeleteSupplierNotesMutation,
  useCreateSupplierNotesMutation,
  useEditSupplierNotesMutation,
  //   useSingleSupplierNotesMutation,
} = supplierNotesDataApiSlice;

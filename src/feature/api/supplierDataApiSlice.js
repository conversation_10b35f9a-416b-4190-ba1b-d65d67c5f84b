import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const supplierDataApiSlice = createApi({
  reducerPath: "supplierDataApiSlice",
  baseQuery,
  tagTypes: ["supplierData", "singleSupplier"],
  endpoints: (builder) => ({
    getSupplierList: builder.query({
      query: (body) => ({
        url: `suppliers/listSupplier`,
        method: "POST",
        body,
      }),
      providesTags: ["supplierData"],
    }),
    listAllSupplier: builder.query({
      query: (body) => ({
        url: `suppliers/listAllSupplier`,
        method: "POST",
        body,
      }),
    }),
    deleteSupplier: builder.mutation({
      query: (body) => ({
        url: `suppliers/deleteSupplier`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["supplierData"],
    }),
    createSupplier: builder.mutation({
      query: (body) => ({
        url: `suppliers/createSupplier`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["supplierData"],
    }),
    editSupplier: builder.mutation({
      query: (body) => ({
        url: `suppliers/editSupplier`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["supplierData", "singleSupplier"],
    }),
    singleSupplier: builder.query({
      query: (body) => ({
        url: `suppliers/singleSupplier`,
        method: "POST",
        body,
      }),
      providesTags: ["singleSupplier"],
    }),
  }),
});

export const {
  useGetSupplierListQuery,
  useListAllSupplierQuery,
  useDeleteSupplierMutation,
  useCreateSupplierMutation,
  useEditSupplierMutation,
  useSingleSupplierQuery,
} = supplierDataApiSlice;

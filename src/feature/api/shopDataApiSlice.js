import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const shopDataApiSlice = createApi({
  reducerPath: "shopDataApiSlice",
  baseQuery,
  tagTypes: ["shopProfile"],
  endpoints: (builder) => ({
    getShopInfo: builder.query({
      query: (body) => ({
        url: `shop/getShopInfo`,
        method: "GET",
        body,
      }),
      providesTags: ["shopProfile"],
    }),
    updateShopInfo: builder.mutation({
      query: (body) => ({
        url: `shop/updateShopInfo`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["shopProfile"],
    }),
  }),
});

export const { useGetShopInfoQuery, useLazyGetShopInfoQuery, useUpdateShopInfoMutation } =
  shopDataApiSlice;

import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const userProfileDataApiSlice = createApi({
  reducerPath: "userProfileDataApiSlice",
  baseQuery,
  tagTypes: [],
  endpoints: (builder) => ({
    getUserProfile: builder.query({
      query: (body) => ({
        url: `common/getUserProfile`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetUserProfileQuery,
} = userProfileDataApiSlice;

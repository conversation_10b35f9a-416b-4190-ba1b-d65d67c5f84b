import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const customerNotesDataApiSlice = createApi({
  reducerPath: "customerNotesDataApiSlice",
  baseQuery,
  tagTypes: ["customerNotesData"],
  endpoints: (builder) => ({
    getCustomerNotesList: builder.query({
      query: (body) => ({
        url: `notes/listNotes`,
        method: "POST",
        body,
      }),
      providesTags: ["customerNotesData"],
    }),
    // listAllCustomerNotes: builder.query({
    //   query: (body) => ({
    //     url: `Notes/listAllCustomerNotes`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
    deleteCustomerNotes: builder.mutation({
      query: (body) => ({
        url: `notes/deleteNote`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["customerNotesData"],
    }),
    createCustomerNotes: builder.mutation({
      query: (body) => ({
        url: `notes/createNotes`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["customerNotesData"],
    }),
    editCustomerNotes: builder.mutation({
      query: (body) => ({
        url: `notes/editNotes`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["customerNotesData"],
    }),
    // singleCustomer: builder.mutation({
    //   query: (body) => ({
    //     url: `customers/singleCustomer`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
  }),
});

export const {
  useGetCustomerNotesListQuery,
  // useListAllCustomerNotesQuery,
  useDeleteCustomerNotesMutation,
  useCreateCustomerNotesMutation,
  useEditCustomerNotesMutation,
  //   useSingleCustomerNotesMutation,
} = customerNotesDataApiSlice;

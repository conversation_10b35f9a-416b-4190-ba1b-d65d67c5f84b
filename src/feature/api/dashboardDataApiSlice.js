import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const dashboardDataApiSlice = createApi({
  reducerPath: "dashboardDataApiSlice",
  baseQuery,
  tagTypes: ["dashboardData"],
  endpoints: (builder) => ({
    getDashboardCardCount: builder.query({
      query: (body) => ({
        url: `dashboard/getSummaryCount`,
        method: "GET",
        body,
      }),
    }),
    getDashboardInventoryChartData: builder.query({
      query: (body) => ({
        url: `dashboard/getInventoryChartData`,
        method: "POST",
        body,
      }),
    }),
    getDashboardInventoryChartYears: builder.query({
      query: (body) => ({
        url: `dashboard/getInventoryChartYears`,
        method: "GET",
        body,
      }),
    }),
  }),
});

export const {
  useGetDashboardCardCountQuery,
  useGetDashboardInventoryChartDataQuery,
  useGetDashboardInventoryChartYearsQuery,
} = dashboardDataApiSlice;

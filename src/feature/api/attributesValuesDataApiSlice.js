import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const attributesValuesDataApiSlice = createApi({
  reducerPath: "attributesValuesDataApiSlice",
  baseQuery,
  tagTypes: ["attributesValuesData"],
  endpoints: (builder) => ({
    getAttributesValues: builder.query({
      query: (body) => ({
        url: `attributesValue/listAttributeValues`,
        method: "POST",
        body,
      }),
      providesTags: ["attributesValuesData"],
    }),
    getAllAttributesValues: builder.query({
      query: (body) => ({
        url: `attributesValue/listAllAttributeValues`,
        method: "POST",
        body,
      }),
      providesTags: ["attributesValuesData"],
    }),
    deleteAttributesValues: builder.mutation({
      query: (body) => ({
        url: `attributesValue/deleteAttributeValue`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["attributesValuesData"],
    }),
    createAttributesValues: builder.mutation({
      query: (body) => ({
        url: `attributesValue/createAttributeValue`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["attributesValuesData"],
    }),
    editAttributesValues: builder.mutation({
      query: (body) => ({
        url: `attributesValue/editAttributeValue`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["attributesValuesData"],
    }),
    singleAttributesValues: builder.mutation({
      query: (body) => ({
        url: `attributesValue/singleAttributeValue`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetAttributesValuesQuery,
  useGetAllAttributesValuesQuery,
  useDeleteAttributesValuesMutation,
  useCreateAttributesValuesMutation,
  useEditAttributesValuesMutation,
  useSingleAttributesValuesMutation,
} = attributesValuesDataApiSlice;

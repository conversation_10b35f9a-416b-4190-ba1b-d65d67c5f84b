import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const statusApiSlice = createApi({
  reducerPath: "statusApiSlice",
  baseQuery,
  endpoints: (builder) => ({
    getGeneralStatus: builder.query({
      query: (body) => ({
        url: `common/getGeneralStatus`,
        method: "GET",
        body,
      }),
    }),
    getUserStatus: builder.query({
      query: (body) => ({
        url: `common/getUserStatus`,
        method: "GET",
        body,
      }),
    }),
    getShopStatus: builder.query({
      query: (body) => ({
        url: `common/getShopStatus`, // Fixed URL
        method: "GET",
        body,
      }),
    }),
    getBranchStatus: builder.query({
      query: (body) => ({
        url: `common/getBranchStatus`,
        method: "GET",
        body,
      }),
    }),
    getStaffStatus: builder.query({
      query: (body) => ({
        url: `common/getStaffStatus`,
        method: "GET",
        body,
      }),
    }),
  }),
});

export const {
  useGetGeneralStatusQuery,
  useGetUserStatusQuery,
  useGetShopStatusQuery,
  useGetBranchStatusQuery,
  useGetStaffStatusQuery,
  useLazyGetGeneralStatusQuery,
  useLazyGetUserStatusQuery,
  useLazyGetShopStatusQuery,
  useLazyGetBranchStatusQuery,
  useLazyGetStaffStatusQuery
} = statusApiSlice;

import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const supplierAttachmentsDataApiSlice = createApi({
  reducerPath: "supplierAttachmentsDataApiSlice",
  baseQuery,
  tagTypes: ["supplierAttachmentsData"],
  endpoints: (builder) => ({
    getSupplierAttachmentsList: builder.query({
      query: (body) => ({
        url: `supplierAttachments/listSupplierAttachments`,
        method: "POST",
        body,
      }),
      providesTags: ["supplierAttachmentsData"],
    }),
    // listAllSupplierAttachments: builder.query({
    //   query: (body) => ({
    //     url: `supplierAttachments/listAllSupplierAttachments`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
    deleteSupplierAttachments: builder.mutation({
      query: (body) => ({
        url: `supplierAttachments/deleteSupplierAttachments`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["supplierAttachmentsData"],
    }),
    createSupplierAttachments: builder.mutation({
      query: (body) => ({
        url: `supplierAttachments/createSupplierAttachment`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["supplierAttachmentsData"],
    }),
    editSupplierAttachments: builder.mutation({
      query: (body) => ({
        url: `supplierAttachments/editAttachment`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["supplierAttachmentsData"],
    }),
    singleSupplier: builder.mutation({
      query: (body) => ({
        url: `supplierAttachments/singleSupplierAttachments`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetSupplierAttachmentsListQuery,
  // useListAllSupplierAttachmentsQuery,
  useDeleteSupplierAttachmentsMutation,
  useCreateSupplierAttachmentsMutation,
  useEditSupplierAttachmentsMutation,
  useSingleSupplierAttachmentsMutation,
} = supplierAttachmentsDataApiSlice;

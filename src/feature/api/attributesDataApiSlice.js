import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const attributesDataApiSlice = createApi({
  reducerPath: "attributesDataApiSlice",
  baseQuery,
  tagTypes: ["attributesData"],
  endpoints: (builder) => ({
    getAttributes: builder.query({
      query: (body) => ({
        url: `attributes/listAttributes`,
        method: "POST",
        body,
      }),
      providesTags: ["attributesData"],
    }),
    listAllAttributes: builder.query({
      query: () => ({
        url: `attributes/listAllAttributes`,
        method: "GET",
      }),
      providesTags: ["attributesData"],
    }),
    deleteAttributes: builder.mutation({
      query: (body) => ({
        url: `attributes/deleteAttribute`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["attributesData"],
    }),
    createAttributes: builder.mutation({
      query: (body) => ({
        url: `attributes/createAttribute`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["attributesData"],
    }),
    editAttributes: builder.mutation({
      query: (body) => ({
        url: `attributes/editAttribute`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["attributesData"],
    }),
    singleAttributes: builder.mutation({
      query: (body) => ({
        url: `attributes/singleAttribute`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetAttributesQuery,
  useListAllAttributesQuery,
  useDeleteAttributesMutation,
  useCreateAttributesMutation,
  useEditAttributesMutation,
  useSingleAttributesMutation,
} = attributesDataApiSlice;

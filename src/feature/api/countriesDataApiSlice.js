import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const countriesDataApiSlice = createApi({
  reducerPath: "countriesDataApiSlice",
  baseQuery,
  endpoints: (builder) => ({
    getCountries: builder.query({
      query: () => ({
        url: `common/getCountries`,
        method: "GET"
      })
    })
  })
});

export const { useGetCountriesQuery, useLazyGetCountriesQuery } = countriesDataApiSlice;

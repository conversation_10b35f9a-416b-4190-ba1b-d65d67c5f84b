import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const subCategoriesDataApiSlice = createApi({
  reducerPath: "subCategoriesDataApiSlice",
  baseQuery,
  tagTypes: ["subCategoriesData"],
  endpoints: (builder) => ({
    getSubCategories: builder.query({
      query: (body) => ({
        url: `subcategories/listSubCategories`,
        method: "POST",
        body,
      }),
      providesTags: ["subCategoriesData"],
    }),
    getAllSubCategories: builder.query({
      query: (body) => ({
        url: `subcategories/listAllSubCategories`,
        method: "POST",
        body,
      }),
      providesTags: ["subCategoriesData"],
    }),
    deleteSubCategories: builder.mutation({
      query: (body) => ({
        url: `subcategories/deleteSubCategory`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["subCategoriesData"],
    }),
    createSubCategories: builder.mutation({
      query: (body) => ({
        url: `subcategories/createSubCategory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["subCategoriesData"],
    }),
    editSubCategories: builder.mutation({
      query: (body) => ({
        url: `subcategories/EditSubCategory`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["subCategoriesData"],
    }),
    singleSubCategories: builder.mutation({
      query: (body) => ({
        url: `subcategories/singleSubCategory`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetSubCategoriesQuery,
  useGetAllSubCategoriesQuery,
  useDeleteSubCategoriesMutation,
  useCreateSubCategoriesMutation,
  useEditSubCategoriesMutation,
  useSingleSubCategoriesMutation,
} = subCategoriesDataApiSlice;

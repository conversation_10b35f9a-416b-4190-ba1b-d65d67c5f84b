import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const invoiceDataApiSlice = createApi({
  reducerPath: "invoiceDataApiSlice",
  baseQuery,
  tagTypes: ["paymentToSupplierInvoiceData"],
  endpoints: (builder) => ({
    getProductList: builder.query({
      query: (body) => ({
        url: `products/listProducts`,
        method: "POST",
        body,
      }),
      providesTags: ["paymentToSupplierInvoiceData"],
    }),
    listAllProduct: builder.query({
      query: (body) => ({
        url: `products/listAllProducts`,
        method: "POST",
        body,
      }),
    }),
    deleteProduct: builder.mutation({
      query: (body) => ({
        url: `products/deleteProduct`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["paymentToSupplierInvoiceData"],
    }),
    createProduct: builder.mutation({
      query: (body) => ({
        url: `products/createProduct`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["paymentToSupplierInvoiceData"],
    }),
    editProduct: builder.mutation({
      query: (body) => ({
        url: `products/editProduct`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["paymentToSupplierInvoiceData"],
    }),
    createUpdateVariationsProducts: builder.mutation({
      query: (body) => ({
        url: `products/createUpdateVariationsProducts`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["paymentToSupplierInvoiceData"],
    }),
    getVariationsProducts: builder.mutation({
      query: (body) => ({
        url: `products/getVariationsProducts`,
        method: "POST",
        body,
      }),
      invalidatesTags: [],
    }),
    singleProduct: builder.mutation({
      query: (body) => ({
        url: `products/singleProduct`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetProductListQuery,
  useListAllProductQuery,
  useDeleteProductMutation,
  useCreateProductMutation,
  useEditProductMutation,
  useCreateUpdateVariationsProductsMutation,
  useGetVariationsProductsMutation,
  useSingleProductMutation,
} = invoiceDataApiSlice;

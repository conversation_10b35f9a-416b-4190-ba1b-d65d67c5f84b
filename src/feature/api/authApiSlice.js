import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const authApiSlice = createApi({
  reducerPath: "authApiSlice",
  baseQuery,
  tagTypes: ["userProfile"],
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (body) => ({
        url: `auth/login`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["userProfile"],
    }),
    getProfile: builder.query({
      query: (body) => ({
        url: `common/getProfile`,
        method: "GET",
        body,
      }),
      providesTags: ["userProfile"],
    }),
    updateProfile: builder.mutation({
      query: (body) => ({
        url: `common/updateProfile`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["userProfile"],
    }),
    changePassword: builder.mutation({
      query: (body) => ({
        url: `common/changePassword`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
  useChangePasswordMutation,
} = authApiSlice;

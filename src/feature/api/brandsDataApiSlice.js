import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const brandsDataApiSlice = createApi({
  reducerPath: "brandsDataApiSlice",
  baseQuery,
  tagTypes: ["brandsData"],
  endpoints: (builder) => ({
    getBrands: builder.query({
      query: (body) => ({
        url: `brands/listBrands`,
        method: "POST",
        body,
      }),
      providesTags: ["brandsData"],
    }),
    listAllBrands: builder.query({
      query: () => ({
        url: `brands/listAllBrands`,
        method: "GET",
      }),
      providesTags: ["brandsData"],
    }),
    deleteBrands: builder.mutation({
      query: (body) => ({
        url: `brands/deleteBrand`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["brandsData"],
    }),
    createBrands: builder.mutation({
      query: (body) => ({
        url: `brands/createBrand`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["brandsData"],
    }),
    editBrands: builder.mutation({
      query: (body) => ({
        url: `brands/editBrand`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["brandsData"],
    }),
    singleBrands: builder.mutation({
      query: (body) => ({
        url: `brands/singleBrand`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetBrandsQuery,
  useListAllBrandsQuery,
  useDeleteBrandsMutation,
  useCreateBrandsMutation,
  useEditBrandsMutation,
  useSingleBrandsMutation,
} = brandsDataApiSlice;

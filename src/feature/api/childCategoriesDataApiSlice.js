import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const childCategoriesDataApiSlice = createApi({
  reducerPath: "childCategoriesDataApiSlice",
  baseQuery,
  tagTypes: ["childCategoriesData"],
  endpoints: (builder) => ({
    getChildCategories: builder.query({
      query: (body) => ({
        url: `childcategories/listChildCategories`,
        method: "POST",
        body,
      }),
      providesTags: ["childCategoriesData"],
    }),
    getAllChildCategories: builder.query({
      query: (body) => ({
        url: `childcategories/listAllChildCategories`,
        method: "POST",
        body,
      }),
      providesTags: ["childCategoriesData"],
    }),
    deleteChildCategories: builder.mutation({
      query: (body) => ({
        url: `childcategories/deleteChildCategory`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["childCategoriesData"],
    }),
    createChildCategories: builder.mutation({
      query: (body) => ({
        url: `childcategories/createChildCategory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["childCategoriesData"],
    }),
    editChildCategories: builder.mutation({
      query: (body) => ({
        url: `childcategories/editChildCategory`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["childCategoriesData"],
    }),
    singleChildCategories: builder.mutation({
      query: (body) => ({
        url: `childcategories/singleChildCategory`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetChildCategoriesQuery,
  useGetAllChildCategoriesQuery,
  useDeleteChildCategoriesMutation,
  useCreateChildCategoriesMutation,
  useEditChildCategoriesMutation,
  useSingleChildCategoriesMutation,
} = childCategoriesDataApiSlice;

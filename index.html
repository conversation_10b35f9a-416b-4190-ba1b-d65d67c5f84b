<!DOCTYPE html>
<html
  lang="en"
  dir="ltr"
  data-bs-theme="dark"
  data-color-theme=""
  data-layout="vertical"
>
  <head>
    <!-- Required meta tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicon icon-->
    <link
      rel="shortcut icon"
      type="image/png"
      href="/src/assets/images/logos/favicon.png"
    />

    <!-- Core Css -->
    <link rel="stylesheet" href="/src/assets/css/styles.css" />
    <title>Technology Lab - ERP</title>
  </head>
  <body>
    <!-- Preloader -->
    <!-- <div class="preloader">
      <img
        src="/src/assets/images/logos/favicon.png"
        alt="loader"
        class="lds-ripple img-fluid"
      />
    </div> -->
    <div id="main-wrapper"></div>
    <script type="module" src="/src/main.jsx"></script>
    <div class="dark-transparent sidebartoggler"></div>

    <!-- Import Js Files -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
      integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script src="/src/assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/src/assets/js/un-customer.js"></script>
    <script src="/src/assets/libs/simplebar/dist/simplebar.min.js"></script>
    <script src="/src/assets/js/theme/app.init.js"></script>
    <script src="/src/assets/js/theme/theme.js"></script>
    <script src="/src/assets/js/theme/app.min.js"></script>
    <script src="/src/assets/js/theme/sidebarmenu.js"></script>
    <script
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCwy8U406aQSAW2OaQcMgPobTwUh22DVGA&libraries=places"
      async
    ></script>

    <!-- solar icons -->
    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
    <!-- <script src="/src/assets/libs/fullcalendar/index.global.min.js"></script> -->
    <!-- <script src="/src/assets/js/apps/calendar-init.js"></script> -->
    <script src="/src/assets/js/vendor.min.js"></script>
    <!-- <script src="/src/assets/libs/apexcharts/dist/apexcharts.min.js"></script> -->
    <!-- <script src="/src/assets/js/dashboards/dashboard3.js"></script> -->
     <script>
     document.addEventListener('wheel', (event) => {
        if (event.target.type === 'number') {
          event.preventDefault();
        }
      }, { passive: false });
      </script>
  </body>
</html>
